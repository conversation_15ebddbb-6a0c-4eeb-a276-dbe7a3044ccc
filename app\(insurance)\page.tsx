import Container from '@/components/Common/Container'
import Image from 'next/image'
import BimeHero from "@/public/assets/images/bime-hero.webp"
import PlateForm from '@/components/Insurance/Main/PlateForm'
import Approval<PERSON>hart from '@/components/Common/svg/Approval<PERSON>hart'
import DocumentListIcon from '@/components/Common/svg/DocumentListIcon'
import SupportIcon from '@/components/Common/svg/SupportIcon'
import HelpOperatorIcon from '@/components/Common/svg/HelpoperatorIcon'
import Faq from '@/components/Common/Faq'
import { getPageContent } from '@/lib/utils'
import { PageContentResponse, ServiceColorVariantType, ServiceStatusType } from '@/lib/types/types'
import type { Metadata } from "next";
import DoubleQoute from "@/public/assets/images/double-qoute.png"
import { BadgePercent, ReceiptText, Star, Umbrella } from 'lucide-react'
import MoneyBagIcon from '@/components/Common/svg/MoneyBagIcon'
import WithdrawIcon from '@/components/Common/svg/WithdrawIcon'
import DisplayServiceIcon from '@/components/Services/DisplayServiceIcon'
import Link from 'next/link'
import ServiceIcon from '@/components/Common/svg/ServiceIcon'
import { CAR_TICKETS_PATH, MOTOR_TICKETS_PATH, CAR_VIOLATION_IMAGE_PATH, CAR_INSURANCE_PATH, DRIVING_LICENSE_STATUS_PATH } from "@/lib/routes"
import Customer1 from "@/public/assets/images/customer-1.png"
import Customer2 from "@/public/assets/images/customer-2.png"
import Iran from "@/public/assets/images/IRAN.webp"
import Dana from "@/public/assets/images/dana.webp"
import Moalem from "@/public/assets/images/MOALLEM.webp"
import Novin from "@/public/assets/images/novin.webp"
import Pasargad from "@/public/assets/images/PASARGAD.webp"
import Saman from "@/public/assets/images/SAMAN.webp"
import Sarmad from "@/public/assets/images/SARMAD.webp"
import Taavon from "@/public/assets/images/TAAVON.webp"
import HomeBlogSection from '@/components/Blog/HomeBlogSection'
import InsuranceInfoTabs from '@/components/Main/InsuranceInfoTabs'


export const metadata: Metadata = {
    robots: {
        index: false,
        follow: false,
    }
};

interface ServiceItemProps {
    href: string;
    name: string;
    icon: React.ReactNode;
    colorVariant?: ServiceColorVariantType;
    status: ServiceStatusType;
    type: "car" | "motor"
}

const ServiceItem: React.FC<ServiceItemProps> = ({ href, name, icon, colorVariant, status }) => {

    const isDisabled = status === 'DEACTIVE'
    return (
        <li className="w-full">
            {isDisabled ? (
                <span className="flex flex-col items-center gap-y-3">
                    <DisplayServiceIcon variant={colorVariant} status={status}>{icon}</DisplayServiceIcon>
                    <Link href={href} className="text-xs text-center px-1">{name}</Link>
                </span>
            ) : (
                <Link href={href} className="flex flex-col items-center gap-y-3">
                    <DisplayServiceIcon variant={colorVariant} status={status}>{icon}</DisplayServiceIcon>
                    <span className="text-xs text-center px-1">{name}</span>
                </Link>
            )}
        </li>
    );
};


const page = async () => {
    const data: PageContentResponse = await getPageContent("car-tickets")

    const { schema, description, faqs, title } = data





    const services: ReadonlyArray<ServiceItemProps> = [
        {
            icon: <ServiceIcon imagePath="car-crash" />,
            href: CAR_TICKETS_PATH, colorVariant: "yellow",
            name: "خلافی خودرو",
            status: "ACTIVE",
            type: "car"
        },
        {
            icon: <ServiceIcon imagePath="motorcycle" />,
            href: MOTOR_TICKETS_PATH,
            colorVariant: "blue",
            name: "خلافی موتور سیکلت",
            status: "ACTIVE",
            type: "motor"
        },
        {
            icon: <ServiceIcon imagePath="service-camera" />,
            href: CAR_VIOLATION_IMAGE_PATH,
            colorVariant: "purple",
            name: "تصویر تخلفات رانندگی",
            status: "ACTIVE",
            type: "car"
        },
        {
            icon: <ServiceIcon imagePath="car-overrun" />,
            href: CAR_INSURANCE_PATH,
            colorVariant: "green",
            name: "استعلام بیمه شخص ثالث",
            status: "ACTIVE",
            type: "car"
        },



        {
            icon: <ServiceIcon imagePath="certificate-service" />,
            href: DRIVING_LICENSE_STATUS_PATH,
            colorVariant: "emerald",
            name: "وضعیت گواهینامه",
            status: "ACTIVE",
            type: "car"
        },
    ]
    return (
        <>
            <div>
                <Container className='md:px-4 md:min-h-[85svh]'>
                    <div className='bg-bime-hero md:mt-10 bg-white max-w-[1280px] flex justify-center items-center h-[550px] md:p-10  rounded-3xl md:shadow-md md:border border-gray-200'>
                        <div className='md:grid md:grid-cols-5 flex flex-col gap-5 h-full max-md:px-4 max-md:mt-20'>
                            <div className='col-span-2 h-[460px] relative z-0 md:top-10 md:-left-5 w-full max-md:top-40 rounded-3xl max-md:order-2 '>
                                <div className='border-[#E4E6E9] border bg-gray-100 p-8 rounded-3xl relative z-10'>
                                    <div className='space-y-3 mb-8'>
                                        <h1 className='md:text-xl font-bold'>
                                            خرید بیمه شخص ثالث خودرو
                                        </h1>
                                        <p className='text-sm font-light'>
                                            پلاک خودرو و کد ملی صاحب پلاک مورد نظر را وارد کنید
                                        </p>
                                    </div>
                                    <PlateForm />

                                </div>
                                <div className='border-[#E4E6E9] border max-md:hidden bg-gray-100/50 h-[410px] absolute z-0 top-10 -left-5 w-full rounded-3xl'>

                                </div>

                            </div>
                            <div className='col-span-3 relative h-full md:w-[90%] mx-auto max-md:hidden'>
                                <Image src={BimeHero} alt='bime-hero' priority fill />

                            </div>
                        </div>

                    </div>
                </Container>
                <section className='md:h-52 bg-[#F5F6F8] max-w-7xl mx-auto mb-10 max-md:px-3'>
                    <div className='text-center mt-10 mb-5'>
                        <h2 className='mb-5 font-light text-2xl'>
                            چرا از خودراکس بخریم؟
                        </h2>
                        <p>
                            تجربه‌ای راحت و امن برای انتخاب بهترین بیمه شخص ثالث
                        </p>
                    </div>
                    <div className='md:grid grid-cols-4  gap-10 max-md:space-y-5'>
                        <div className='bg-white p-5 border border-gray-300 rounded-3xl flex flex-col gap-5 items-center'>
                            <div
                                className="w-20 h-20 bg-cover bg-center flex items-center justify-center"
                                style={{ backgroundImage: "url('/assets/images/icon-bg.png')" }}
                            >
                                <ApprovalChart size={35} />
                            </div>
                            <h3 className='text-lg'>
                                صدور فوری بیمه نامه
                            </h3>
                            <p className='text-center font-light '>
                                بیمه‌نامه شما در همان روز صادر و قابل استفاده خواهد بود.
                            </p>

                        </div>
                        <div className='bg-white p-5 border border-gray-300 rounded-3xl flex flex-col gap-5 items-center'>
                            <div
                                className="w-20 h-20 bg-cover bg-center flex items-center justify-center"
                                style={{ backgroundImage: "url('/assets/images/icon-bg.png')" }}
                            >
                                <DocumentListIcon />
                            </div>
                            <h3 className='text-lg'>
                                تنوع کامل شرکت‌های بیمه
                            </h3>
                            <p className='text-center font-light '>
                                امکان انتخاب از میان تمامی شرکت‌های معتبر بیمه‌ای کشور.
                            </p>

                        </div>
                        <div className='bg-white p-5 border border-gray-300 rounded-3xl flex flex-col gap-5 items-center'>
                            <div
                                className="w-20 h-20 bg-cover bg-center flex items-center justify-center"
                                style={{ backgroundImage: "url('/assets/images/icon-bg.png')" }}
                            >
                                <SupportIcon fill='green' />
                            </div>
                            <h3 className='text-lg'>
                                پشتیبانی ۲۴/۷
                            </h3>
                            <p className='text-center font-light '>
                                هر روز هفته و در هر ساعت، همراه شما هستیم.
                            </p>

                        </div>
                        <div className='bg-white p-5 border border-gray-300 rounded-3xl flex flex-col gap-5 items-center'>
                            <div
                                className="w-20 h-20 bg-cover bg-center flex items-center justify-center"
                                style={{ backgroundImage: "url('/assets/images/icon-bg.png')" }}
                            >
                                <HelpOperatorIcon />
                            </div>
                            <h3 className='text-lg'>
                                مشاوره تخصصی رایگان
                            </h3>
                            <p className='text-center font-light '>
                                کارشناسان ما بهترین گزینه بیمه را متناسب با شرایط شما پیشنهاد می‌دهند.
                            </p>

                        </div>
                    </div>

                </section>

            </div>
            <section className='bg-white max-md:px-3'>
                <div className="mx-auto pt-16 md:px-3">
                    <Faq faqs={faqs} className="mt-16" />

                </div>
                <div className='max-w-7xl mx-auto mt-10 bg-primary p-5 rounded-3xl text-white flex flex-col justify-center items-center gap-5 py-10'>
                    <Image src={DoubleQoute} alt='double-qoute' />
                    <h2 className='md:text-xl'>
                        الزام قانونی بیمه شخص ثالث
                    </h2>
                    <p>
                        بیمه شخص ثالث برای تمام وسایل نقلیه اجباری است و حتی یک روز نداشتن آن، جریمه مالی به همراه دارد
                    </p>
                </div>
                <div className=' md:mx-auto mt-10 bg-gradient-to-b from-white to-[#F5F6F8] md:p-10 max-md:w-full max-md:mb-5'>
                    <div className='md:max-w-7xl md:mx-auto flex flex-col items-center justify-center gap-10 max-md:w-full'>
                        <div className='text-center max-md:w-full'>
                            <h2 className='md:text-2xl mb-3'>
                                مراحل خرید بیمه شخص ثالث
                            </h2>
                            <p className='font-light'>
                                پاسخ به مهم‌ترین سوالاتی که هنگام خرید بیمه شخص ثالث برای کاربران پیش می‌آید
                            </p>
                        </div>
                        <div className='md:grid grid-cols-3 gap-10 max-md:w-full max-md:space-y-5'>
                            <div className='bg-white p-5 border max-md:w-full border-gray-300 rounded-3xl flex flex-col gap-5 items-center'>
                                <div
                                    className="w-20 h-20 bg-cover bg-center flex items-center justify-center"
                                    style={{ backgroundImage: "url('/assets/images/icon-bg.png')" }}
                                >
                                    <span className="w-10 h-10 flex items-center justify-center text-2xl bg-gradient-to-l from-[#20A7F4] to-[#1FA4F3]  text-white rounded-full">
                                        1
                                    </span>

                                </div>
                                <h3 className='text-lg'>
                                    استعلام قیمت بهترین بیمه
                                </h3>
                                <p className='text-center font-light '>
                                    با امکان استعلام و مقایسه قیمت معتبرترین شرکت‌های بیمه، بهترین بیمه را انتخاب کنید.
                                </p>

                            </div>
                            <div className='bg-white p-5 border border-gray-300 rounded-3xl flex flex-col gap-5 items-center'>
                                <div
                                    className="w-20 h-20 bg-cover bg-center flex items-center justify-center"
                                    style={{ backgroundImage: "url('/assets/images/icon-bg.png')" }}
                                >
                                    <span className="w-10 h-10 flex items-center justify-center text-2xl bg-gradient-to-l from-[#F8BE0C] to-[#FFD246]  text-white rounded-full">
                                        2
                                    </span>

                                </div>
                                <h3 className='text-lg'>
                                    تکمیل و بارگذاری مدارک
                                </h3>
                                <p className='text-center font-light '>
                                    تصاویر مدارک مورد نیاز مانند گواهینامه،کارت ماشین و بیمه‌نامه قبلی خود را بارگذاری کنید.
                                </p>

                            </div>
                            <div className='bg-white p-5 border border-gray-300 rounded-3xl flex flex-col gap-5 items-center'>
                                <div
                                    className="w-20 h-20 bg-cover bg-center flex items-center justify-center"
                                    style={{ backgroundImage: "url('/assets/images/icon-bg.png')" }}
                                >
                                    <span className="w-10 h-10 flex items-center justify-center text-2xl bg-gradient-to-l from-[#5C9183] to-[#94C8BA]  text-white rounded-full">
                                        3
                                    </span>

                                </div>
                                <h3 className='text-lg'>
                                    صدور و دریافت بیمه‌نامه
                                </h3>
                                <p className='text-center font-light '>
                                    پس از بررسی مدارک توسط کارشناسان بیمه‌بازار، بیمه‌نامه صادره را در همان روز تحویل بگیرید.
                                </p>

                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section className='bg-[#EBEBEB] h-80 '>
                <div className='max-w-7xl flex justify-center items-center flex-col mx-auto p-10 font-light'>
                    <h2 className='mb-3 text-2xl'>
                        نیاز به مشاوره و راهنمایی دارید؟
                    </h2>
                    <p className='mb-5 text-justify max-md:leading-8'>
                        شماره تماس و شماره پلاک خود را وارد نمایید تا کارشناسان آیتول در اسرع وقت با شما تماس حاصل نمایند.
                    </p>
                    <button className='bg-[#363A3E] text-white p-2 px-6 rounded-xl'>
                        درخواست مشاوره
                    </button>
                </div>
            </section>
           <InsuranceInfoTabs />
            <section className='max-w-7xl mx-auto flex flex-col justify-center items-center gap-10 mb-5 mt-14'>
                <div className='text-center'>
                    <h2 className='md:text-2xl mb-3'>
                        سرویس های مشابه دیگر ما
                    </h2>
                    <p className='font-light'>
                        با سرویس‌های متنوع ما، انتخاب‌های بیشتری در اختیار دارید.
                    </p>
                </div>
                <div className='w-full mx-auto'>
                    <ul className="w-full flex justify-between">
                        {services.map((service, index) => (
                            (
                                <ServiceItem
                                    key={index}
                                    {...service}
                                />
                            )
                        ))}
                    </ul>
                </div>
            </section>
            <section className="bg-primary p-10 mt-10">
                <div className="md:max-w-7xl mx-auto">
                    <div className="text-white text-center mb-10">
                        <h2 className="md:text-2xl mb-3">خودروکس از نگاه مشتریان</h2>
                        <p className="font-light">همیشه برای خواندن نظرات شما آماده‌ایم.</p>
                    </div>

                    <div className="max-md:overflow-x-auto max-md:whitespace-nowrap">
                        <div className="flex gap-6 px-4 md:justify-between md:px-0">
                           
                            <div className=" min-w-[280px] max-md:min-h-40 text-white bg-[#343E50] flex flex-col p-5 rounded-3xl gap-5 max-md:h-auto">
                                <div className="flex gap-5 items-center">
                                    <div>
                                        <Image src={Customer1} alt="customer-1" />
                                    </div>
                                    <div>
                                        <h3>فاطمه سپهری</h3>
                                        <div className="flex items-center gap-1 text-sm mt-2">
                                            <Star size={18} fill="#F7BC06" color="#F7BC06" />
                                            <Star size={18} fill="#F7BC06" color="#F7BC06" />
                                            <Star size={18} fill="#F7BC06" color="#F7BC06" />
                                            <Star size={18} fill="#F7BC06" color="#F7BC06" />
                                            <Star size={18} fill="gray" color="gray" />
                                        </div>
                                    </div>
                                </div>
                                <div className="font-light px-1">
                                    <p className='whitespace-normal text-justify leading-7'>
                                        بدون دغدغه زمان و مکان، خرید بیمه را انجام دادم و این خیلی عالیه. قیمت خودروکس از بقیه جاها کمتر بود و علاوه بر اون کد تخفیف هم داده بودن
                                    </p>
                                </div>
                            </div>

                            <div className="min-w-[280px] max-md:min-h-40 bg-white text-[#343E50] flex flex-col p-5 rounded-3xl gap-5">
                                <div className="flex gap-5 items-center">
                                    <div>
                                        <Image src={Customer2} alt="customer-2" />
                                    </div>
                                    <div>
                                        <h3>محمد سپهری</h3>
                                        <div className="flex items-center gap-1 text-sm mt-2">
                                            <Star size={18} fill="#F7BC06" color="#F7BC06" />
                                            <Star size={18} fill="#F7BC06" color="#F7BC06" />
                                            <Star size={18} fill="#F7BC06" color="#F7BC06" />
                                            <Star size={18} fill="#F7BC06" color="#F7BC06" />
                                            <Star size={18} fill="gray" color="gray" />
                                        </div>
                                    </div>
                                </div>
                                <div className="font-light px-1">
                                    <p className='whitespace-normal text-justify leading-7'>
                                        بدون دغدغه زمان و مکان، خرید بیمه را انجام دادم و این خیلی عالیه. قیمت خودروکس از بقیه جاها کمتر بود و علاوه بر اون کد تخفیف هم داده بودن
                                    </p>
                                </div>
                            </div>

                            <div className=" min-w-[280px] max-md:min-h-40 text-white bg-[#343E50] flex flex-col p-5 rounded-3xl gap-5">
                                <div className="flex gap-5 items-center">
                                    <div>
                                        <Image src={Customer1} alt="customer-3" />
                                    </div>
                                    <div>
                                        <h3>فاطمه سپهری</h3>
                                        <div className="flex items-center gap-1 text-sm mt-2">
                                            <Star size={18} fill="#F7BC06" color="#F7BC06" />
                                            <Star size={18} fill="#F7BC06" color="#F7BC06" />
                                            <Star size={18} fill="#F7BC06" color="#F7BC06" />
                                            <Star size={18} fill="#F7BC06" color="#F7BC06" />
                                            <Star size={18} fill="gray" color="gray" />
                                        </div>
                                    </div>
                                </div>
                                <div className="font-light px-1">
                                    <p className='whitespace-normal text-justify leading-7'>
                                        بدون دغدغه زمان و مکان، خرید بیمه را انجام دادم و این خیلی عالیه. قیمت خودروکس از بقیه جاها کمتر بود و علاوه بر اون کد تخفیف هم داده بودن
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>


            <div className="mx-auto pt-16 px-3">
                <Faq faqs={faqs} className="mt-16" />

            </div>
            <section className='max-w-7xl mx-auto py-12 mb-10 max-md:px-3'>
                <div className='text-center mb-10'>
                    <h2 className='md:text-2xl mb-3'>
                        بیمه‌های تحت پوشش خودروکس
                    </h2>
                    <p className='font-light'>
                        تمامی شرکت‌های معتبر بیمه‌ای کشور در خودروکس در دسترس شما هستند
                    </p>
                </div>
                <div className='flex justify-between mt-8 gap-3 max-md:flex-wrap'>
                    <div className='bg-white p-5 rounded-2xl w-[20%] flex justify-center'>
                        <Image src={Iran} alt='iran' />
                    </div>
                    <div className='bg-white p-5 rounded-2xl w-[20%] flex justify-center'>
                        <Image src={Dana} alt='dana' />
                    </div>
                    <div className='bg-white p-5 rounded-2xl w-[20%] flex justify-center'>
                        <Image src={Moalem} alt='moalem' />
                    </div>
                    <div className='bg-white p-5 rounded-2xl w-[20%] flex justify-center'>
                        <Image src={Novin} alt='novin' />
                    </div>
                    <div className='bg-white p-5 rounded-2xl w-[20%] flex justify-center'>
                        <Image src={Pasargad} alt='pasargad' />
                    </div>
                    <div className='bg-white p-5 rounded-2xl w-[20%] flex justify-center'>
                        <Image src={Saman} alt='saman' />
                    </div>
                    <div className='bg-white p-5 rounded-2xl w-[20%] flex justify-center'>
                        <Image src={Sarmad} alt='sarmad' />
                    </div>
                    <div className='bg-white p-5 rounded-2xl w-[20%] flex justify-center'>
                        <Image src={Taavon} alt='taavon' />
                    </div>
                </div>
            </section>
            <section className='bg-[#F5F6F8] py-5 md:py-10'>
                <HomeBlogSection />
            </section>

        </>
    )
}

export default page