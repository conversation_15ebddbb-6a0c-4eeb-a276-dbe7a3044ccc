import { ReactNode } from "react"
import Image from "next/image"
import patternSvg from "@/public/assets/images/pattern.webp"

type Props = {
  children: ReactNode
  center?: boolean
  className?: string
  backgroundHeight?: number
}

export default function Container({
  children,
  center,
  className = "",
  backgroundHeight,
}: Props) {
  const bgHeight = backgroundHeight ? backgroundHeight : center ? "" : ""

  return (
    <div
      className={`relative z-0 ${center ? "max-md:h-[100svh]" : ""}`}
    >
      <div className="inset-0 z-0">
        <Image
          src={patternSvg}
          alt="Background pattern"
          fill
          priority
          sizes="100vh"
        />
      </div>

      <div className="z-10 relative">
        <div className="w-full py-32 bg-gray-100 z-[-10] absolute left-0 bottom-0 broken-div" />

        <div
          className={`w-full px-2 md:px-0 flex justify-center max-md:min-h-screen ${className} ${
            center
              ? "items-center max-md:!min-h-screen"
              : "items-start pt-4 md:pt-8"
          }`}
        >
          {children}
        </div>
      </div>
    </div>
  )
}
