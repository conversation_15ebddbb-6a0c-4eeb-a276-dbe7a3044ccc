import InsuranceFianalDetails from '@/components/Insurance/IsuranceFormPage/InsuranceFianalDetails'
import InsuranceFormDetails from '@/components/Insurance/IsuranceFormPage/InsuranceFormDetails'
import PaymentMethod from '@/components/Insurance/IsuranceFormPage/PaymentMethod'
import ShippingMethod from '@/components/Insurance/IsuranceFormPage/ShippingMethod'
import CheckoutProgress from '@/components/Insurance/IsuranceFormPage/CheckoutProgress'
import { Metadata } from 'next'
import React from 'react'
export const metadata: Metadata = {
    robots: {
        index: false,
        follow: false,
    }
};

const stepsList = [
    { title: "اطلاعات بیمه " },
    { title: "نحوه ارسال" },
    { title: "تایید اطلاعات" },
    { title: "پرداخت" },
];

const page = async ({
    searchParams,
}: {
    searchParams: Promise<{ step?: string }>;
}) => {
    const { step } = await searchParams;
    const currentStep = Number(step ?? "1");

    
    const renderCurrentStep = () => {
        switch (currentStep) {
            case 1:
                return <InsuranceFormDetails />;
            case 2:
                return <ShippingMethod />;
            case 3:
                return <InsuranceFianalDetails />;
            case 4:
                return <PaymentMethod />;
            default:
                return <InsuranceFormDetails />;
        }
    };

   
    const steps = stepsList.map((s, index) => {
        const stepIndex = index + 1;

        let status: "completed" | "current" | "upcoming" = "upcoming";
        if (stepIndex < currentStep) status = "completed";
        else if (stepIndex === currentStep) status = "current";

        return {
            ...s,
            status,
        };
    });

    return (
        <div>
            <div className="max-w-7xl mx-auto px-5 mt-10">
                <CheckoutProgress steps={steps} />
                {renderCurrentStep()}
            </div>
        </div>
    );
};

export default page