import React from "react";
import clsx from "clsx";

type StepBarProps = {
  steps: number;
  currentStep: number;
};

const StepBar: React.FC<StepBarProps> = ({ steps, currentStep }) => {
  return (
    <div className="flex items-center w-full">
      {Array.from({ length: steps }).map((_, index) => {
        const stepNumber = index + 1;
        const isActive = stepNumber === currentStep;
        const isCompleted = stepNumber < currentStep;

        return (
          <React.Fragment key={index}>
            
            <div
              className={clsx(
                "h-2 rounded-full flex-1 transition-all duration-300",
                isCompleted
                  ? "bg-yellow-400"
                  : isActive
                  ? "bg-yellow-400"
                  : "bg-gray-200"
              )}
            ></div>

            
            {stepNumber !== steps && <div className="w-2" />}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default StepBar;
