import { <PERSON><PERSON><PERSON><PERSON>, MapPin } from 'lucide-react'
import Iran from "@/public/assets/images/IRAN.webp"
import Image from "next/image";
import Link from 'next/link';
import CarCart1 from "@/public/assets/images/car-cart-1.png"
import CarCart2 from "@/public/assets/images/car-cart-2.png"
import CarCart3 from "@/public/assets/images/car-cart-3.png"
import InsurancePdf from "@/public/assets/images/insurance-pdf.png"
const page = () => {
    return (
        <div className='max-md:px-3'>
            <section className='mt-10 bg-white rounded-3xl border md:p-10 md:max-w-7xl mx-auto max-md:px-3 max-md:py-3 '>
                <div className='md:grid max-md:flex max-md:flex-col grid-cols-12 gap-5 md:w-[90%] mx-auto'>
                    <div className='grid grid-cols-3 gap-5 max-md:gap-2 col-span-7 font-light max-md:order-2 max-md:text-sm'>
                        <div className='bg-muted border rounded-3xl max-md:rounded-2xl max-md:space-y-1  p-5 max-md:p-2'>
                            <p className='text-muted-foreground'>
                                تاریخ سفارش
                            </p>
                            <p>
                                19 دی1404
                            </p>
                        </div>
                        <div className='bg-muted border rounded-3xl max-md:rounded-2xl max-md:space-y-1  p-5 max-md:p-2 max-md:whitespace-nowrap'>
                            <p className='text-muted-foreground'>
                                وضعیت پرداخت
                            </p>
                            <p>
                                پرداخت شده
                            </p>
                        </div>
                        <div className='bg-muted border rounded-3xl max-md:rounded-2xl max-md:space-y-1  p-5 max-md:p-2'>
                            <p className='text-muted-foreground'>
                                وضعیت بیمه
                            </p>
                            <p>
                                صادر شده
                            </p>
                        </div>
                        <div className="group border col-span-3 group-hover:border-primary p-5 max-md:px-3 rounded-3xl transition-colors duration-300 hover:border-primary">
                            <div className="flex gap-3">
                                <div>
                                    <MapPin className="transition-colors duration-300 group-hover:stroke-primary" size={24} />
                                </div>
                                <div className="flex flex-col gap-3 text-sm w-full">
                                    <p className="md:text-lg transition-colors duration-300 group-hover:text-primary">
                                        تهران، خیابان طالقانی، کوچه شهید بهشتی، پلاک 41
                                    </p>
                                    <div className="text-xs text-gray-400 flex flex-col gap-2">
                                        <p>کد پستی: <span>311416165</span></p>
                                        <p>گیرنده: <span>محمد محمدی</span></p>
                                    </div>
                                    <div className="flex items-center justify-between w-full bg-muted p-5 max-md:hidden rounded-2xl">
                                        <button className="bg-primary text-white p-2 px-5 rounded-xl">
                                            دریافت بیمه نامه
                                        </button>
                                        <button className='bg-warning rounded-xl p-2 px-5'>
                                            تمدید بیمه نامه
                                        </button>
                                        <button className='border p-2 px-5 rounded-xl'>
                                            فرم نظرسنجی
                                        </button>
                                    </div>
                                </div>
                            </div>
                                <div className=" md:hidden mt-5 grid grid-cols-2 gap-3  items-center justify-between w-full bg-muted p-5 max-md:p-3 rounded-2xl">
                                    <button className="bg-primary text-white p-2 px-5 rounded-xl">
                                        دریافت بیمه نامه
                                    </button>
                                    <button className='bg-warning rounded-xl p-2 px-5'>
                                        تمدید بیمه نامه
                                    </button>
                                    <button className='border p-2 px-5 rounded-xl col-span-2'>
                                        فرم نظرسنجی
                                    </button>
                                </div>
                        </div>
                    </div>
                    <div className='col-span-5 border  p-5 rounded-3xl bg-white shadow-2xs max-md:order-1'
                        style={{
                            backgroundImage: "url('/assets/images/half-circle-yellow.png')",
                            backgroundRepeat: "no-repeat",
                            // backgroundPosition: "8% 50%",
                        }}
                    >
                        <div className='flex gap-3 items-center'>
                            <span className='text-warning bg-warning w-4 h-2 rounded-4xl'>
                            </span>
                            <h2>
                                بیمه نامه خریداری شده
                            </h2>
                        </div>
                        <div className="p-3 border-2 flex gap-5 items-center border-dashed rounded-3xl mt-10 divide-x-2">
                            <div className="md:w-[25%]">
                                <Image src={Iran} alt="iran" width={55} height={55} />
                            </div>
                            <div className="px-5 space-y-3">
                                <p className="flex gap-2">
                                    <span className="font-bold">بیمه گذار: </span>
                                    <span>محمد محمدی</span>
                                </p>
                                <p className="flex gap-2">
                                    <span className="font-bold">
                                        نوع بیمه:
                                    </span>
                                    <span>
                                        بیمه شخص ثالث
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div className='mt-5 flex flex-col gap-3 font-light'>
                            <div className='flex justify-between'>
                                <p>
                                    پلاک
                                </p>
                                <p>
                                    ایران 25 | 742 الف 45
                                </p>
                            </div>
                            <div className='flex justify-between'>
                                <p>
                                    مدت بیمه نامه
                                </p>
                                <p>
                                    یک سال
                                </p>
                            </div>
                            <div className='flex justify-between'>
                                <p>
                                    مبلغ بیمه نامه
                                </p>
                                <p>
                                    10,000,000 تومان
                                </p>
                            </div>
                        </div>

                    </div>

                </div>

            </section>
            <section className='mt-10 bg-white rounded-3xl border p-10 max-md:px-3 max-md:p-5 max-w-7xl mx-auto'>
                <div>
                    <h2 className='md:text-xl font-bold mb-5 pb-3 relative w-fit title-bt-border'>
                        اطلاعات  بیمه نامه
                    </h2>
                </div>
                <div className='grid grid-cols-3 max-md:grid-cols-2 gap-5 max-md:gap-3 font-light max-md:text-sm'>
                    <div className='flex flex-col gap-2 bg-muted p-5 max-md:p-3 max-md:rounded-2xl rounded-3xl border max-md:w-full '>
                        <p>
                            پوشش مالی:
                        </p>
                        <p>
                            53.3 میلیون تومان
                        </p>
                    </div>

                    <div className='flex flex-col gap-2 bg-muted p-5 max-md:p-3 max-md:rounded-2xl rounded-3xl border '>
                        <p>
                            پوشش نقص عضو:
                        </p>
                        <p>
                            53.3 میلیارد تومان
                        </p>
                    </div>
                    <div className='flex md:flex-col max-md:justify-between max-md:col-span-2 gap-2 bg-muted p-5 max-md:p-3 max-md:rounded-2xl rounded-3xl border'>
                        <p>
                            پوشش حوادث راننده:
                        </p>
                        <p>
                            1.3 میلیارد تومان
                        </p>
                    </div>
                    <div className='flex justify-between max-md:flex-wrap gap-2 bg-muted p-5 max-md:p-3 max-md:rounded-2xl rounded-3xl border'>
                        <p>
                            تخفیف حوادث راننده جدید
                        </p>
                        <p>
                            15 درصد
                        </p>
                    </div>
                    <div className='flex justify-between max-md:flex-wrap gap-2 bg-muted p-5 max-md:p-3 max-md:rounded-2xl rounded-3xl border'>
                        <p>
                            تخفیف حوادث راننده جدید
                        </p>
                        <p>
                            15 درصد
                        </p>
                    </div>
                    <div className='flex justify-between max-md:col-span-2 gap-2 bg-muted p-5 max-md:p-3 max-md:rounded-2xl rounded-3xl border'>
                        <p>
                            بیمه گذار مالک خودرو میباشد
                        </p>
                        <p>
                            بله
                        </p>
                    </div>
                    <div className='flex justify-between gap-2 bg-muted p-5 max-md:p-3 max-md:rounded-2xl rounded-3xl border'>
                        <p>
                            تاریخ خرید
                        </p>
                        <p>
                            1400/01/01
                        </p>
                    </div>
                    <div className='flex justify-between gap-2 bg-muted p-5 max-md:p-3 max-md:rounded-2xl rounded-3xl border'>
                        <p>
                            تاریخ صدور
                        </p>
                        <p>
                            1400/01/01
                        </p>
                    </div>
                    <div className='flex justify-between max-md:col-span-2 gap-2 bg-muted p-5 max-md:p-3 max-md:rounded-2xl rounded-3xl border'>
                        <p>
                            کد رهگیری
                        </p>
                        <p>
                            kh5454675
                        </p>
                    </div>
                    <div className='p-5 max-md:col-span-2 max-md:p-3 max-md:rounded-2xl max-md:py-5 md:col-span-3 bg-muted border border-dashed rounded-3xl font-light insurance-bg'
                    >
                        <div className='mb-5'>
                            <h3 className='font-bold  border-b pb-1 border-gray-300 w-fit'>
                                اطلاعات بیمه گذار
                            </h3>
                        </div>
                        <div className='md:divide-x md:flex md:gap-10 max-md:grid max-md:gap-5 max-md:grid-cols-2'>
                            <div className='flex flex-col gap-2 md:px-10'>
                                <div className='flex gap-2 items-center'>
                                    <p className='w-2 h-2 bg-gray-700 rounded-full'></p>
                                    <span>
                                        نام و نام خانوادگی
                                    </span>
                                </div>
                                <p className='text-gray-500 flex gap-2'>
                                    <span className='font-bold'>-</span> محمد محمدی
                                </p>
                            </div>
                            <div className='flex flex-col gap-2 md:px-10'>
                                <div className='flex gap-2 items-center'>
                                    <p className='w-2 h-2 bg-gray-700 rounded-full'></p>
                                    <span>
                                        کد ملی
                                    </span>
                                </div>
                                <p className='text-gray-500 flex gap-2'>
                                    <span className='font-bold'>-</span> 1278225485
                                </p>
                            </div>

                            <div className='flex flex-col gap-2 md:px-10'>
                                <div className='flex gap-2 items-center'>
                                    <p className='w-2 h-2 bg-gray-700 rounded-full'></p>
                                    <span>
                                        تاریخ تولد
                                    </span>
                                </div>
                                <p className='text-gray-500 flex gap-2'>
                                    <span className='font-bold'>-</span> 1400/01/01
                                </p>
                            </div>
                            <div className='flex flex-col gap-2 md:px-10'>
                                <div className='flex gap-2 items-center'>
                                    <p className='w-2 h-2 bg-gray-700 rounded-full'></p>
                                    <span>
                                        شماره موبایل
                                    </span>
                                </div>
                                <p className='text-gray-500 flex gap-2'>
                                    <span className='font-bold'>-</span> 09123456789
                                </p>
                            </div>

                        </div>

                    </div>
                    <div className=' bg-muted p-5 max-md:col-span-2 max-md:p-3 max-md:rounded-2xl rounded-3xl border'>
                        <h3 className='font-bold  border-b pb-1 border-gray-300 w-fit'>
                            اطلاعات خودرو
                        </h3>
                        <div className='flex flex-col gap-5 mt-5'>
                            <div className='flex justify-between'>
                                <p className='text-muted-foreground'>
                                    نوع خودرو
                                </p>
                                <p>
                                    سواری
                                </p>
                            </div>
                            <div className='flex justify-between'>
                                <p className='text-muted-foreground'>
                                    برند خودرو
                                </p>
                                <p>
                                    پژو
                                </p>
                            </div>
                            <div className='flex justify-between'>
                                <p className='text-muted-foreground'>
                                    مدل خودرو
                                </p>
                                <p>
                                    206
                                </p>
                            </div>
                            <div className='flex justify-between'>
                                <p className='text-muted-foreground'>
                                    کاربری
                                </p>
                                <p>
                                    شخصی
                                </p>
                            </div>
                            <div className='flex justify-between'>
                                <p className='text-muted-foreground'>
                                    نوع سوخت
                                </p>
                                <p>
                                    بنزین
                                </p>
                            </div>
                            <div className='flex justify-between'>
                                <p className='text-muted-foreground'>
                                    سال ساخت
                                </p>
                                <p>
                                    1394
                                </p>
                            </div>
                            <div className='flex justify-between'>
                                <p className='text-muted-foreground'>
                                    نوع خودرو
                                </p>
                                <p>
                                    چهار سلندر
                                </p>
                            </div>
                            <div className='flex justify-between'>
                                <p className='text-muted-foreground'>
                                    پلاک
                                </p>
                                <p>
                                    ایران 25 | 742 الف 45
                                </p>
                            </div>

                        </div>
                    </div>
                    <div className='col-span-2 bg-muted p-5 max-md:p-3 max-md:rounded-2xl rounded-3xl border'>
                        <h3 className='font-bold  border-b pb-1 border-gray-300 w-fit'>
                            اطلاعات بیمه نامه قبلی
                        </h3>
                        <div className='flex flex-col gap-5 mt-5'>
                            <div className='flex justify-between'>
                                <p className='text-muted-foreground'>
                                    وضعیت بیمه نامه قبلی
                                </p>
                                <p className='max-md:whitespace-normal max-md:w-[40%]'>
                                    بیمه نامه دارم با شماره پلاک مالک فعلی خودرو
                                </p>
                            </div>
                            <div className='flex justify-between'>
                                <p className='text-muted-foreground'>
                                    شرکت بیمه گر قبلی
                                </p>
                                <p>
                                    بیمه ایران
                                </p>
                            </div>
                            <div className='flex justify-between'>
                                <p className='text-muted-foreground'>
                                    مدت بیمه نامه قبلی
                                </p>
                                <p>
                                    یک سال
                                </p>
                            </div>
                            <div className='flex justify-between'>
                                <p className='text-muted-foreground'>
                                    تاریخ اتمام بیمه قبلی
                                </p>
                                <p>
                                    1402/01/01
                                </p>
                            </div>
                            <div className='flex justify-between'>
                                <p className='text-muted-foreground'>
                                    تخفیف ثالث قبلی
                                </p>
                                <p>
                                    10%
                                </p>
                            </div>
                            <div className='flex justify-between'>
                                <p className='text-muted-foreground'>
                                    تخفیف حوادث راننده قبلی
                                </p>
                                <p>
                                    10%
                                </p>
                            </div>
                            <div className='flex justify-between'>
                                <p className='text-muted-foreground'>
                                    جریمه دیرکرد
                                </p>
                                <p>
                                    0
                                </p>
                            </div>

                        </div>

                    </div>
                </div>
            </section>
            <section className='mt-10 bg-white rounded-3xl border p-10 max-md:p-5 max-md:px-3 max-w-7xl mx-auto'>
                <div>
                    <h2 className='text-xl font-bold mb-5 pb-3 relative w-fit title-bt-border'>
                        الحاقیه
                    </h2>
                </div>
                <div className='flex justify-center items-center gap-8 flex-col'>
                    <p className='text-center leading-8'>
                        برای اعمال هرگونه تغیر روی بیمه نامه خود، درخواست الحاقیه ثبت کنید.
                        <Link className='text-primary' href={"#"}>بیشتر بدانید</Link>
                    </p>
                    <button className='flex items-center gap-2 border px-10 py-4 bg-muted  rounded-2xl'>
                        <ArchiveRestore size={20} />   درخواست الحاقیه
                    </button>
                </div>
            </section>
            <section className='mt-10 bg-white rounded-3xl border p-10 max-md:p-5 max-md:px-3 max-w-7xl mx-auto mb-10'>
                <div>
                    <h2 className='text-xl font-bold mb-5 pb-3 relative w-fit title-bt-border'>
                        اطلاعات و مدارک بیمه گزار
                    </h2>
                </div>
                <div className='flex gap-10 max-md:justify-between max-md:gap-0 max-md:gap-y-5 mt-10 max-md:mt-5 text-sm max-md:flex-wrap max-md:text-xs'>
                    <div className='flex flex-col gap-3 items-center max-md:w-[48%] max-md:h-full'>
                        <Image src={CarCart1} alt='car-cart' className='max-md:w-full max-md:h-full' />
                        <h2>
                            عکس پشت کارت ماشین یا برگ سبز
                        </h2>
                    </div>
                    <div className='flex flex-col gap-3 items-center max-md:w-[48%] max-md:h-full'>
                        <Image src={CarCart2} alt='car-cart' className='max-md:w-full max-md:h-full' />
                        <h2>
                            عکس روی کارت ماشین یا برگ سبز
                        </h2>
                    </div>
                    <div className='flex flex-col gap-3 items-center max-md:w-[48%] max-md:h-full'>
                        <Image src={CarCart3} alt='car-cart' className='max-md:w-full max-md:h-full' />
                        <h2 className=''>
                            عکس روی کارت ماشین یا برگ سبز
                        </h2>
                    </div>
                    <div className='flex flex-col gap-3 items-center max-md:w-[48%] max-md:h-full'>
                        <Image src={InsurancePdf} alt='car-cart' className='max-md:w-full max-md:h-full' />
                        <h2 className=''>
                            عکس بیمه نامه
                        </h2>
                    </div>
                </div>
            </section>
        </div>
    )
}

export default page