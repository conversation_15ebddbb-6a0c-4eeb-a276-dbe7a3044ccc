"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import SwiperCore from "swiper";
import "swiper/css";
import "swiper/css/navigation";
import Image from "next/image";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import Link from "next/link";
import EmptyCover from "@/public/assets/images/empty-cover.webp";
import { Article } from "@/lib/types/articles-types";

type Props = {
  blogPosts: Article[];
};

export default function BlogSlider({ blogPosts }: Props) {
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const swiperRef = useRef<SwiperCore | null>(null);

  useEffect(() => {
    const swiper = swiperRef.current;
    if (!swiper) return;

    const updateSlideOpacity = () => {
      const { activeIndex, slides } = swiper;
      const slidesPerView = swiper.params.slidesPerView;

      slides.forEach((slide, index) => {
        const isRight = index >= activeIndex;
        const isFirstVisibleSlide = index === activeIndex;
        const el = slide.querySelector(".blog-overlay");

        if (el) {
          el.classList.remove("!block", "lg:!block", "bg-gradient-to-r", "bg-gradient-to-l");
        }

        if (activeIndex !== 0 && isFirstVisibleSlide && isRight && el) {
          el.classList.add("lg:!block", "bg-gradient-to-l");
        }

        if (
          el &&
          index !== slides.length - 1 &&
          index === activeIndex + (slidesPerView ? Math.ceil(+slidesPerView) - 1 : 0)
        ) {
          el.classList.add("!block", "bg-gradient-to-r");
        }
      });
    };

    swiper.on("slideChange", updateSlideOpacity);
    updateSlideOpacity();

    return () => {
      swiper.off("slideChange", updateSlideOpacity);
    };
  }, []);

  return (
    <div className="relative w-full max-w-7xl mx-auto">
      <Swiper
        onSwiper={(swiper) => (swiperRef.current = swiper)}
        modules={[Navigation]}
        spaceBetween={20}
        slidesPerView={3}
        navigation={{
          nextEl: ".swiper-button-next-custom",
          prevEl: ".swiper-button-prev-custom",
        }}
        onInit={(swiper) => {
          setIsBeginning(swiper.isBeginning);
          setIsEnd(swiper.isEnd);
        }}
        onSlideChange={(swiper) => {
          setIsBeginning(swiper.isBeginning);
          setIsEnd(swiper.isEnd);
        }}
        breakpoints={{
          0: { slidesPerView: 1 },
          360: { slidesPerView: 1.5 },
          640: { slidesPerView: 2.5 },
          1024: { slidesPerView: 4 },
        }}
      >
        {blogPosts.map((post, index) => (
          <SwiperSlide key={index}>
            <Link href={`/blog/${post.slug}`} target="_blank" className="relative block group">
              <div className="blog-overlay hidden absolute left-0 top-0 right-0 bottom-0 from-white to-transparent z-10"></div>
              <div className="bg-white rounded-2xl px-4 py-4">
                <div className="relative">
                  <div className="w-full h-[162px] relative rounded-2xl overflow-hidden">
                    <Image
                      src={post.cover || EmptyCover}
                      alt={post.title}
                      width={300}
                      height={200}
                      className="!w-full !h-full object-cover"
                    />
                  </div>
                  <div className="absolute bottom-[-8px] left-[15px] flex items-center bg-[#E7EBEF] justify-between rounded-xl overflow-hidden">
                    <span className="bg-gray-400 transition-colors duration-300 group-hover:bg-yellow pr-1 pl-2 rounded-bl-xl text-xs block rounded-r-xl py-1 text-white">
                      {post.date}
                    </span>
                    <span className="bg-[#E7EBEF] text-xs rounded-l-xl pr-1 pl-2">
                      {post.comments_count} نظر{" "}
                    </span>
                  </div>
                </div>
                <div className="w-full flex mt-4 flex-col gap-y-2">
                  <h3 className="text-sm text-gray-600">{post.title}</h3>
                  <div className="relative w-[100px] h-[2px] bg-gray-300">
                    <div className="absolute top-0 right-0 w-[30%] h-full bg-gray-500 transition-colors duration-300 group-hover:bg-yellow"></div>
                  </div>
                  {post.summary && (
                    <p className="accent-gray-400 leading-relaxed text-xs">
                      {post.summary.length <= 70
                        ? post.summary
                        : `${post.summary.slice(0, 70)} ...`}
                    </p>
                  )}
                </div>
              </div>
            </Link>
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom navigation buttons */}
      <div
        className={`swiper-button-next-custom hidden lg:block absolute border-2 bg-primary border-[#E6E6E8] lg:left-[-5px] xl:left-[-50px] top-1/2 transform -translate-y-1/2 z-20 p-1 md:p-2 rounded-full shadow-lg cursor-pointer transition ${
          isEnd ? "!bg-[#FFFFFF]" : ""
        }`}
      >
        <ChevronLeft
          size={20}
          color={!isBeginning && !isEnd ? "white" : isBeginning ? "white" : "gray"}
        />
      </div>
      <div
        className={`swiper-button-prev-custom hidden lg:block absolute border bg-primary border-[#E6E6E8] lg:right-[-5px] xl:right-[-50px] top-1/2 transform -translate-y-1/2 z-20 p-1 md:p-2 rounded-full shadow-lg cursor-pointer transition ${
          isBeginning ? "!bg-[#FFFFFF]" : ""
        }`}
      >
        <ChevronRight
          size={20}
          color={!isBeginning && !isEnd ? "white" : isEnd ? "white" : "grey"}
        />
      </div>
    </div>
  );
}
