import type { Metadata } from "next";
import localFont from "next/font/local";

import "./globals.css";
import NextTopLoader from "nextjs-toploader";
// import Footer from "@/components/Footer/Footer";
// import Navbar from "@/components/Header/Navbar";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};


const myCustomFont = localFont({
  src: [
    {
      path: "../public/assets/fonts/iransans/IRANSansWeb_Light.woff2", // relative to this file
      weight: "300",
      style: "normal",
    },
    {
      path: "../public/assets/fonts/iransans/IRANSansWeb_Medium.woff2", // relative to this file
      weight: "500",
      style: "normal",
    },
    {
      path: "../public/assets/fonts/iransans/IRANSansWeb_Bold.woff2",
      weight: "700",
      style: "normal",
    },
  ],
  display: "swap",
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fa" className={myCustomFont.className} dir="rtl">
      <body
        
      >
        {/* <Navbar /> */}
        <NextTopLoader />
        {children}
        {/* <Footer /> */}
      </body>
    </html>
  );
}
