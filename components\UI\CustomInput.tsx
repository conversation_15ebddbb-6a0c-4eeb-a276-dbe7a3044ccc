import React, { HTMLInputAutoCompleteAttribute, HTMLInputTypeAttribute } from "react";

interface CustomInputProps {
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  align?: "left" | "center" | "right";
  variant?: "primary" | "secondary";
  direction?: "ltr" | "rtl";
  allowOnlyNumbers?: boolean;
  onChange?: (value: string) => void;
  value?: string | number;
  maxLength?: number;
  type?: HTMLInputTypeAttribute | undefined;
  className?: string;
  inputMode?: "none" | "text" | "tel" | "url" | "email" | "numeric" | "decimal" | "search" | undefined;
  placeholder?: string;
  autoFocus?: boolean;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  ref?: React.Ref<HTMLInputElement> | undefined;
  autoComplete?: HTMLInputAutoCompleteAttribute | undefined;
}

const variantClasses: Record<string, string> = {
  primary: "border-gray-300 focus:ring-gray-400",
  secondary: "border-gray-300 bg-muted focus:ring-gray-400",
};

const CustomInput: React.FC<CustomInputProps> = ({
  leftIcon,
  maxLength,
  rightIcon,
  align = "right",
  variant = "primary",
  className = "",
  direction = "ltr",
  allowOnlyNumbers = true,
  onChange,
  ref,
  value = "",
  ...props
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    let inputValue = event.target.value;
    if (allowOnlyNumbers) {
      inputValue = inputValue.replace(/[^0-9]/g, "");
    }
    if (maxLength && inputValue.length > maxLength) return;
    onChange?.(inputValue);
  };

  const baseClasses =
    "w-full px-5 h-[65px] text-sm md:text-base py-2 border placeholder:text-sm placeholder:muted-foreground text-primary-foreground rounded-2xl";

  const alignmentClass =
    align === "center"
      ? "text-center pl-14 pr-14"
      : align === "right"
      ? "text-right"
      : "text-left";

  const paddingLeft = leftIcon && align !== "center" ? "pl-14" : "pl-5";
  const paddingRight = rightIcon && align !== "center" ? "pr-14" : "pr-5";
  const directionClass = direction === "ltr" ? "left-direction" : "";

  const inputClassName = [
    baseClasses,
    variantClasses[variant] || variantClasses.primary,
    paddingLeft,
    paddingRight,
    alignmentClass,
    directionClass,
    className,
  ]
    .filter(Boolean)
    .join(" ")
    .trim();

  return (
    <div className="relative flex items-center w-full">
      {leftIcon && <div className="absolute left-5">{leftIcon}</div>}
      <input
        ref={ref}
        dir={direction}
        className={inputClassName}
        value={value}
        onChange={handleChange}
        {...props}
      />
      {rightIcon && <div className="absolute right-5">{rightIcon}</div>}
    </div>
  );
};

export default CustomInput;
