"use client";

import { FC, useState, useRef, useEffect } from "react";
import { SlidersHorizontal, ChevronDown } from "lucide-react";

interface SortOption {
  label: string;
  value: string;
}

interface SortbarProps {
  onSortChange?: (sortValue: string) => void;
  resultCount?: number;
}

const sortOptions: SortOption[] = [
  { label: "همه", value: "all" },
  { label: "ارزان ترین", value: "cheapest" },
  { label: "گران ترین", value: "expensive" },
  { label: "بیشترین توانایی", value: "best_ability" },
  { label: "بیشترین تعداد شعب", value: "most_branches" },
];

/**
 * Sortbar Component
 *
 * Enhanced sortbar with mobile dropdown functionality.
 * Features:
 * - Desktop: Horizontal sort options
 * - Mobile: Dropdown with selected option display
 * - Click outside to close dropdown
 * - Smooth animations
 *
 * @param onSortChange - Callback when sort option changes
 * @param resultCount - Number of results to display
 */
const Sortbar: FC<SortbarProps> = ({ onSortChange, resultCount = 31 }) => {
  const [selectedSort, setSelectedSort] = useState<SortOption>(sortOptions[0]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSortSelect = (option: SortOption) => {
    setSelectedSort(option);
    setIsDropdownOpen(false);
    onSortChange?.(option.value);
  };

  return (
    <div className="bg-white border flex-1 max-md:max-w-full max-md:px-3 py-4 md:py-8 w-full md:col-span-8 mr-auto gap-3 flex items-center px-5 rounded-3xl relative">
      {/* Desktop Label */}
      <div className="hidden md:flex gap-1 text-black items-center whitespace-nowrap md:text-base text-xs font-bold">
        <SlidersHorizontal size={16} />
        مرتب سازی:
      </div>

      {/* Desktop Sort Options */}
      <ul className="hidden md:flex flex-wrap gap-0 lg:gap-x-1 md:text-base text-sm">
        {sortOptions.map((option) => (
          <li
            key={option.value}
            onClick={() => handleSortSelect(option)}
            className={`cursor-pointer py-2 px-3 whitespace-nowrap md:text-base text-xs font-light rounded-3xl transition-colors ${
              selectedSort.value === option.value
                ? 'bg-primary text-white'
                : 'text-[#5E646B] hover:bg-gray-100'
            }`}
          >
            {option.label}
          </li>
        ))}
      </ul>

      {/* Mobile Dropdown */}
      <div className="md:hidden flex-1 relative" ref={dropdownRef}>
        <button
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          className="w-52 bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 flex items-center justify-between text-sm hover:bg-gray-100 transition-colors"
        >
          <div className="flex items-center gap-2">
            <SlidersHorizontal size={16} className="text-gray-600" />
            <span className="text-gray-700">{selectedSort.label}</span>
          </div>
          <ChevronDown
            size={16}
            className={`text-gray-600 transition-transform duration-200 ${
              isDropdownOpen ? 'rotate-180' : ''
            }`}
          />
        </button>

        {/* Mobile Dropdown Menu */}
        {isDropdownOpen && (
          <div className="absolute w-52 top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg z-40 overflow-hidden">
            {sortOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => handleSortSelect(option)}
                className={`w-full text-right p-3 hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0 ${
                  selectedSort.value === option.value ? 'bg-primary/5 text-primary font-medium' : 'text-gray-700'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Results Count - Mobile */}
      <div className="md:hidden text-sm text-gray-600 whitespace-nowrap">
        ({resultCount}) بیمه
      </div>

      {/* Results Count - Desktop */}
      <div className="bg-gradient-to-l max-lg:hidden absolute top-1/2 transform -translate-y-1/2 w-[150px] left-0 from-gray-100 to-transparent mr-20 py-3 rounded-3xl">
        <div className="flex justify-center items-center w-full">
          <span className="text-gray-800 text-sm">({resultCount})</span>
          <span className="text-[#9DA5B0] text-sm mr-1">بیمه</span>
        </div>
      </div>
    </div>
  );
};

export default Sortbar;
