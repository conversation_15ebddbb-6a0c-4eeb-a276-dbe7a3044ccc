"use client"
import { ChevronUpIcon } from 'lucide-react'


const SmoothScrollButton = () => {

    const scrollToTop = () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      };
    return (
        <button title='scroll to top' onClick={scrollToTop} type="button" className="bg-warning flex justify-center items-center rounded-full w-12 h-12 p-2">
            <ChevronUpIcon />
        </button>
    )
}

export default SmoothScrollButton