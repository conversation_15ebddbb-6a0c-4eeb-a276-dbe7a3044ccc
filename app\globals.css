@import "tailwindcss";
@config "../tailwind.config.ts";

@layer base {
  :root {
    --background: #f5f6f8;
    --foreground: #62676E;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: #1F84FB;
    --primary-foreground: #0A0A0C;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: #F9FAFB;
    --muted-foreground: #9DA5B0;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: #15192A;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: #E4E6E9;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    /* Default border color for Tailwind v4 */
    --color-border: #E4E6E9;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Apply default border color to all border utilities */
  * {
    border-color: var(--color-border);
  }
}

button {
  cursor: pointer;
}

.broken-div {
  clip-path: polygon(0 100%, 100% 100%, 100% 0, 50% 20%, 0 0);
}

.title-bt-border::before {
  position: absolute;
  content: '';
  background-color: #F7BC06;
  height: 3px;
  right: 0;
  bottom: 0;
  z-index: 10;
  width: 40%;
}

.title-bt-border::after {
  position: absolute;
  content: '';
  background-color: #ebebeb;
  height: 3px;
  right: 0;
  bottom: 0;
  width: 100%;
}

.title-border::after {
    content: '';
    position: absolute;
    bottom: -5;
    right: 0;
    width: 2.5rem;
    height: 2px;
    background: #1F84FB;
}
.title-border::before {
    content: '';
    position: absolute;
    bottom: -5;
    right: 0;
    width: 6rem;
    height: 2px;
    background: #F3F3F3;
}

.insurance-bg {
    background-image: url('/assets/images/insurance-bg.png');
    background-repeat: no-repeat;
    background-position: 8% 50%;
}

@media screen and (max-width: 768px) {
  .bg-bime-hero {
    background-image: url('/assets/images/bime-hero.webp');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: 0 -10px;
  }
  .insurance-bg {
    background-size: 20%;
    background-position: 8% 10%;
  }

}