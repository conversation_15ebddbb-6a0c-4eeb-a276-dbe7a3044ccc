"use client";
import { FilterIcon, Search, ChevronDown, X } from 'lucide-react'
import { useState, useRef, useEffect } from 'react'

interface FilterCategoryProps {
    filters: { label: string }[]
    onFilterSelect?: (filter: { label: string }) => void
    onSearch?: (searchTerm: string) => void
    searchPlaceholder?: string
    resultCount?: number
    resultLabel?: string
}

/**
 * FilterCategory Component
 *
 * A responsive filter component that displays category filters and search functionality.
 * On desktop: Shows filters horizontally with a search input
 * On mobile: Shows filters in a dropdown and search as an animated expandable input
 *
 * @param filters - Array of filter objects with label property
 * @param onFilterSelect - Callback when a filter is selected
 * @param onSearch - Callback when search term changes
 * @param searchPlaceholder - Placeholder text for search input
 * @param resultCount - Number of results to display
 * @param resultLabel - Label for the result count
 */
const FilterCategory = ({
    filters,
    onFilterSelect,
    onSearch,
    searchPlaceholder = "جستجو",
    resultCount = 21,
    resultLabel = "سوال"
}: FilterCategoryProps) => {
    const [selectedFilter, setSelectedFilter] = useState(filters[0])
    const [isDropdownOpen, setIsDropdownOpen] = useState(false)
    const [isSearchOpen, setIsSearchOpen] = useState(false)
    const [searchTerm, setSearchTerm] = useState("")

    const dropdownRef = useRef<HTMLDivElement>(null)
    const searchInputRef = useRef<HTMLInputElement>(null)

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsDropdownOpen(false)
            }
        }

        document.addEventListener('mousedown', handleClickOutside)
        return () => document.removeEventListener('mousedown', handleClickOutside)
    }, [])

    // Focus search input when opened
    useEffect(() => {
        if (isSearchOpen && searchInputRef.current) {
            searchInputRef.current.focus()
        }
    }, [isSearchOpen])

    /**
     * Handle filter selection
     */
    const handleFilterSelect = (filter: { label: string }) => {
        setSelectedFilter(filter)
        setIsDropdownOpen(false)
        onFilterSelect?.(filter)
    }

    /**
     * Handle search input changes
     */
    const handleSearchChange = (value: string) => {
        setSearchTerm(value)
        onSearch?.(value)
    }

    /**
     * Toggle search input on mobile
     */
    const toggleSearch = () => {
        if (isSearchOpen) {
            setSearchTerm("")
            onSearch?.("")
        }
        setIsSearchOpen(!isSearchOpen)
    }

    return (
        <div className="relative">
            {/* Mobile Search Overlay */}
            <div className={`
                md:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200
                transform transition-transform duration-300 ease-out
                ${isSearchOpen ? 'translate-y-0' : '-translate-y-full'}
            `}>
                <div className="flex items-center p-4 gap-3">
                    <div className="flex-1 relative">
                        <input
                            ref={searchInputRef}
                            type="text"
                            value={searchTerm}
                            onChange={(e) => handleSearchChange(e.target.value)}
                            placeholder={searchPlaceholder}
                            className="w-full py-3 bg-[#F9FAFB] pl-4 pr-10 text-gray-500 border border-gray-300 rounded-3xl outline-none focus:ring-2 focus:ring-primary/20"
                        />
                        <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                            <Search className="w-5 h-5 text-gray-400" />
                        </div>
                    </div>
                    <button
                        onClick={toggleSearch}
                        className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                        aria-label="بستن جستجو"
                    >
                        <X className="w-6 h-6 text-gray-600" />
                    </button>
                </div>
            </div>

            {/* Main Filter Container */}
            <div className="flex justify-between items-center max-md:p-3 w-full">
                <div className="flex  md:flex-row gap-3 md:gap-5 items-center h-auto md:h-10">
                    {/* Desktop Filter Label */}
                    <div className="flex items-center gap-2 max-md:hidden">
                        <FilterIcon />
                        <span>مرتب سازی:</span>
                    </div>

                    {/* Desktop Filters */}
                    <div className="hidden md:flex md:gap-8 h-full">
                        {filters.map((filter) => {
                            const isActive = selectedFilter.label === filter.label
                            return (
                                <button
                                    key={filter.label}
                                    onClick={() => handleFilterSelect(filter)}
                                    className={`transition-all cursor-pointer hover:text-primary ${
                                        isActive ? 'text-primary border-b-2 border-primary' : ''
                                    }`}
                                >
                                    {filter.label}
                                    <span className={`mr-2 rounded-full text-sm px-2 border ${isActive ? 'bg-yellow-300 text-gray-600 border-yellow-300' : ''} bg-[#F9FAFB] text-gray-400`}>
                                        2
                                    </span>
                                </button>
                            )
                        })}
                    </div>

                    {/* Mobile Dropdown */}
                    <div className="md:hidden w-[12rem]" ref={dropdownRef}>
                        <button
                            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                            className="w-full flex items-center justify-between gap-2 p-3 bg-[#F9FAFB] border border-gray-300 rounded-xl text-right"
                        >
                            <span className="flex items-center gap-2">
                                <FilterIcon className="w-4 h-4" />
                                {selectedFilter.label}
                            </span>
                            <ChevronDown className={`w-4 h-4 transition-transform ${
                                isDropdownOpen ? 'rotate-180' : ''
                            }`} />
                        </button>

                        {/* Dropdown Menu */}
                        {isDropdownOpen && (
                            <div className="absolute top-full left-5 right-5 mt-1 bg-white border border-gray-200 rounded-xl shadow-lg z-40 overflow-hidden">
                                {filters.map((filter) => (
                                    <button
                                        key={filter.label}
                                        onClick={() => handleFilterSelect(filter)}
                                        className={`w-full text-right p-3 hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0 ${
                                            selectedFilter.label === filter.label ? 'bg-primary/5 text-primary' : ''
                                        }`}
                                    >
                                        {filter.label}
                                    </button>
                                ))}
                            </div>
                        )}
                    </div>
                     <button
                        onClick={toggleSearch}
                        className="md:hidden p-2 hover:bg-gray-100 rounded-full transition-colors border border-gray-300"
                        aria-label="باز کردن جستجو"
                    >
                        <Search className="w-5 h-5 text-gray-600" />
                    </button>
                </div>

                {/* Search and Results */}
                <div className="flex md:gap-8 items-center">
                    {/* Desktop Search */}
                    <div className="relative max-md:hidden">
                        <input
                            type="text"
                            value={searchTerm}
                            onChange={(e) => handleSearchChange(e.target.value)}
                            placeholder={searchPlaceholder}
                            className="w-full py-3 bg-[#F9FAFB] pl-4 pr-10 text-gray-500 border border-gray-300 rounded-3xl outline-none focus:ring-2 focus:ring-primary/20"
                        />
                        <div className="absolute left-5 top-1/2 transform -translate-y-1/2 cursor-pointer">
                            <Search className="w-5 h-5 text-gray-400" />
                        </div>
                    </div>

                    {/* Mobile Search Icon */}
                   

                    {/* Results Count */}
                    <p className='p-3 bg-gradient-to-l from-[#F9FAFB] rounded-3xl'>
                        ({resultCount}) {resultLabel}
                    </p>
                </div>
            </div>
        </div>
    )
}

export default FilterCategory