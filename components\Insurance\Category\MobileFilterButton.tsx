"use client";

import { Filter } from "lucide-react";

interface MobileFilterButtonProps {
  onClick: () => void;
  activeFiltersCount?: number;
  className?: string;
}

/**
 * MobileFilterButton Component
 * 
 * A floating action button for opening the mobile filter drawer.
 * Features:
 * - Fixed positioning at bottom of screen
 * - Badge showing active filter count
 * - Smooth animations and hover effects
 * - Follows Material Design FAB patterns
 * 
 * @param onClick - Callback when button is clicked
 * @param activeFiltersCount - Number of active filters to show in badge
 * @param className - Additional CSS classes
 */
const MobileFilterButton: React.FC<MobileFilterButtonProps> = ({
  onClick,
  activeFiltersCount = 0,
  className = "",
}) => {
  return (
    <button
      onClick={onClick}
      className={`
        fixed bottom-6 right-6 z-30 md:hidden
        bg-primary hover:bg-primary/90 text-white
        w-14 h-14 rounded-full shadow-lg hover:shadow-xl
        flex items-center justify-center
        transition-all duration-300 ease-out
        transform hover:scale-105 active:scale-95
        ${className}
      `}
      aria-label="باز کردن فیلترها"
    >
      <Filter className="w-6 h-6" />
      
      {/* Active Filters Badge */}
      {activeFiltersCount > 0 && (
        <div className="absolute -top-2 -left-2 bg-red-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center animate-pulse">
          {activeFiltersCount > 9 ? '9+' : activeFiltersCount}
        </div>
      )}
    </button>
  );
};

export default MobileFilterButton;
