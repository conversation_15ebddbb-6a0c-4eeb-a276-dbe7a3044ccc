{"name": "khodrox-bime", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.15", "clsx": "^2.1.1", "lodash.debounce": "^4.0.8", "lucide-react": "^0.544.0", "next": "15.5.4", "nextjs-toploader": "^3.9.17", "rc-slider": "^11.1.8", "react": "19.1.0", "react-dom": "19.1.0", "react-multi-date-picker": "^4.5.2", "swiper": "^11.2.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/lodash.debounce": "^4.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.4", "tailwindcss": "^4", "typescript": "^5"}}