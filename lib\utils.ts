import clsx, { ClassValue } from "clsx";
import { apiClient } from "./apiClient";
import { PageContentResponse } from "./types/articles-types";
import { twMerge } from "tailwind-merge";

export async function getPageContent(pageAddress: string): Promise<PageContentResponse> {
  const res = await apiClient(`article/${pageAddress}`,{
    next: { revalidate: 60 }
  });
  const json = await res.json();
  return json.data;
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatWithComma(value: string) {
    if (!value || isNaN(Number(value))) return value
    return Number(value.replace(/,/g, "")).toLocaleString();
}