"use client";

import { useState, useEffect } from "react";
import { X, Filter, ChevronDown, ChevronUp, Car } from "lucide-react";
import Image from "next/image";
import AccordionHeader from "@/components/UI/AccordionHeader";
import InsurancePriceFilter from "./InsurancePriceFilter";
import Checkbox from "./Checkbox";

// Import images (you may need to adjust these paths)
import Iran from "@/public/assets/images/IRAN.webp";
import Dana from "@/public/assets/images/dana.webp";
import Moalem from "@/public/assets/images/MOALLEM.webp";
import Novin from "@/public/assets/images/novin.webp";

interface MobileFilterDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onApplyFilters?: () => void;
  onResetFilters?: () => void;
}

/**
 * MobileFilterDrawer Component
 * 
 * A mobile-optimized filter drawer for insurance category page.
 * Features:
 * - Smooth slide-up animation
 * - Sticky header with close button
 * - Scrollable filter content
 * - Apply/Reset buttons at bottom
 * - Follows existing design patterns
 * 
 * @param isOpen - Controls drawer visibility
 * @param onClose - Callback when drawer is closed
 * @param onApplyFilters - Callback when filters are applied
 * @param onResetFilters - Callback when filters are reset
 */
const MobileFilterDrawer: React.FC<MobileFilterDrawerProps> = ({
  isOpen,
  onClose,
  onApplyFilters,
  onResetFilters,
}) => {
  const [activeFilters, setActiveFilters] = useState(0);

  // Prevent body scroll when drawer is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleApplyFilters = () => {
    onApplyFilters?.();
    onClose();
  };

  const handleResetFilters = () => {
    setActiveFilters(0);
    onResetFilters?.();
  };

  return (
    <>
      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 transition-opacity duration-300"
          onClick={onClose}
        />
      )}

      {/* Drawer */}
      <div
        className={`
          fixed bottom-0 left-0 right-0 z-50 bg-white rounded-t-3xl 
          transform transition-transform duration-300 ease-out h-[90vh]
          ${isOpen ? 'translate-y-0' : 'translate-y-full'}
        `}
      >
        {/* Sticky Header */}
        <div className="sticky top-0 bg-white rounded-t-3xl  border-gray-100 z-10">
          {/* Handle Bar */}
          <div className="flex justify-center pt-3 pb-2">
            <div className="w-12 h-1 bg-gray-300 rounded-full"></div>
          </div>

          {/* Header Content */}
          <div className="flex items-center justify-between px-5 pb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-full">
                <Filter className="w-5 h-5 text-primary" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-gray-900">فیلترها</h2>
                {activeFilters > 0 && (
                  <p className="text-sm text-gray-500">{activeFilters} فیلتر فعال</p>
                )}
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="w-6 h-6 text-gray-600" />
            </button>
          </div>
        </div>

        {/* Scrollable Content */}
        <div className="overflow-y-auto px-5 pb-24" style={{ maxHeight: 'calc(85vh - 120px)' }}>
          {/* Car Info Section */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-4 mb-5 border border-blue-100">
            <h3 className="text-sm font-medium text-gray-800 mb-3">بیمه شخص ثالث خودرو</h3>
            <div className="flex gap-3 items-center">
              <div className="p-2 bg-white rounded-xl shadow-sm">
                <Car size={24} color="#596068" />
              </div>
              <div className="text-sm space-y-1">
                <p className="text-gray-700">
                  <span>پژو پارس</span>
                  <span className="text-gray-500 mr-2">مدل: 1394</span>
                </p>
                <p className="text-gray-500">
                  تاریخ اتمام بیمه: 12 / 1394
                </p>
              </div>
            </div>
            <button className="w-full mt-3 bg-white/80 hover:bg-white text-gray-700 py-2 px-4 rounded-xl border border-gray-200 text-sm transition-colors">
              ویرایش اطلاعات خودرو
            </button>
          </div>

          {/* Discount Code */}
          <div className="bg-white border border-gray-200 rounded-2xl p-4 mb-4">
            <AccordionHeader title="کد تخفیف دارم" showToggleBtn={true}>
              <input
                type="text"
                className="w-full bg-gray-50 rounded-xl p-3 border border-gray-200 text-gray-700 placeholder-gray-400"
                placeholder="کد تخفیف را وارد کنید"
              />
            </AccordionHeader>
          </div>

          {/* Price Range */}
          <div className="bg-white border border-gray-200 rounded-2xl p-4 mb-4">
            <AccordionHeader title="محدوده قیمت" showToggleBtn={true}>
              <InsurancePriceFilter min={0} max={1000000000} minRange={10000000} maxRange={700000000} />
            </AccordionHeader>
          </div>

          {/* Insurance Duration */}
          <div className="bg-white border border-gray-200 rounded-2xl p-4 mb-4">
            <AccordionHeader title="مدت اعتبار بیمه نامه" showToggleBtn={true}>
              <div className="space-y-3">
                {[
                  { id: "6month", label: "6 ماهه" },
                  { id: "1year", label: "1 ساله" },
                  { id: "3month", label: "3 ماهه" },
                  { id: "1month", label: "1 ماهه" },
                  { id: "9month", label: "9 ماهه" },
                ].map((option) => (
                  <Checkbox
                    key={option.id}
                    id={option.id}
                    label={option.label}
                  />
                ))}
              </div>
            </AccordionHeader>
          </div>

          {/* Installment Payment */}
          <div className="bg-white border border-gray-200 rounded-2xl p-4 mb-4">
            <label className="flex items-center justify-between cursor-pointer">
              <span className="text-sm font-medium text-gray-900">امکان پرداخت قسطی</span>
              <div className="relative">
                <input type="checkbox" className="sr-only peer" />
                <div className="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </div>
            </label>
          </div>

          {/* Insurance Companies */}
          <div className="bg-white border border-gray-200 rounded-2xl p-4 mb-4">
            <AccordionHeader title="شرکت بیمه" showToggleBtn={true}>
              <div className="space-y-4">
                {[
                  { id: "iran", name: "بیمه ایران", image: Iran },
                  { id: "dana", name: "بیمه دانا", image: Dana },
                  { id: "moalem", name: "بیمه معلم", image: Moalem },
                  { id: "novin", name: "بیمه نوین", image: Novin },
                ].map((company) => (
                  <label key={company.id} htmlFor={company.id} className="flex items-center justify-between cursor-pointer p-2 hover:bg-gray-50 rounded-lg transition-colors">
                    <div className="flex items-center gap-3">
                      <Image width={24} height={24} src={company.image} alt={company.name} className="rounded" />
                      <span className="text-sm text-gray-700">{company.name}</span>
                    </div>
                    <input type="checkbox" id={company.id} className="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-2" />
                  </label>
                ))}
              </div>
            </AccordionHeader>
          </div>
        </div>

        {/* Sticky Bottom Actions */}
        <div className="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-100 p-4">
          <div className="flex gap-3">
            <button
              onClick={handleResetFilters}
              className="flex-1 py-3 px-4 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors font-medium"
            >
              پاک کردن همه
            </button>
            <button
              onClick={handleApplyFilters}
              className="flex-1 py-3 px-4 bg-primary text-white rounded-xl hover:bg-primary/90 transition-colors font-medium"
            >
              اعمال فیلتر
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default MobileFilterDrawer;
