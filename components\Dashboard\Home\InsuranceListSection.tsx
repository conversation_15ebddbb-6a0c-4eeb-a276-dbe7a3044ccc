"use client"
import FilterCategory from '@/components/UI/FilterCategory'
import { CircleCheckBig, CircleX, Eye, FolderDown, RotateCcw, Umbrella } from 'lucide-react'
import Image from 'next/image'
import Iran from "@/public/assets/images/IRAN.webp"

const InsuranceListSection = () => {
    return (
        <section className='bg-white border p-5 rounded-3xl'>
            <div className='flex gap-3 items-center'>
                <div className='p-3 bg-gradient-to-t from-background to-transparent rounded-b-full'>
                    <Umbrella />
                </div>
                <h1 className=''>
                    لیست بیمه های من
                </h1>
            </div>
            <div className='mt-5'>
                <FilterCategory 
                    filters={[{ label: 'همه' }, { label: 'بیمه شخص ثالث' }, { label: 'بیمه موتور' }]}
                    resultCount={21}
                    resultLabel="بیمه"
                    searchPlaceholder="جستجوی بیمه"
                    onSearch={(searchTerm) => console.log('searching for', searchTerm)}
                    onFilterSelect={(filter) => console.log('filtering by', filter.label)}                    

                />
            </div>
            <div className='mt-5 space-y-5'>
                <div className='flex justify-between items-center p-3 rounded-3xl border bg-muted'>
                    <div className='border-2 border-dashed p-5 rounded-3xl bg-white'>
                        <Image src={Iran} alt='iran' className='w-full h-auto' />
                    </div>
                    <div className='grid grid-cols-1 gap-5 w-full px-5 font-light'>
                        <div className='grid grid-cols-3'>
                            <p>
                                <span>
                                    بیمه گذار:
                                </span>
                                <span className='font-bold'>
                                    محمد محمدی
                                </span>
                            </p>
                            <p>
                                <span>
                                    تاریخ خرید: 
                                </span>
                                <span>
                                    1400/01/01
                                </span>
                            </p>
                            <p className='flex gap-2 items-center '>
                                <span>
                                    وضعیت: 
                                </span>
                                <span className='flex gap-2 items-center text-green-600'>
                                 <CircleCheckBig size={16} />   صادر شده
                                </span>
                            </p>
                        </div>
                        <div className='grid grid-cols-3'>
                            <p>
                                <span>
                                    نوع بیمه: 
                                </span>
                                <span className='font-bold'>
                                    بیمه شخص ثالث
                                </span>
                            </p>
                            <p>
                                <span>
                                    وسیله: 
                                </span>
                                <span>
                                    پژو پارس
                                </span>
                            </p>
                            <p className='flex gap-1'>
                                <span>
                                    پلاک: 
                                </span>
                                <span>
                                    ایران 25 | 742 الف 45
                                </span>
                            </p>
                        </div>
                    </div>
                    <div className='flex justify-between items-center w-96 bg-white p-3 py-5 rounded-2xl'>
                        <button className='flex gap-2 items-center bg-primary text-white p-2 font-light rounded-xl'>
                        <Eye />    جزئیات بیمه
                        </button>
                        <button className='bg-warning text-white p-2 rounded-xl'>
                            <FolderDown />
                        </button>
                        <button className='bg-black text-white p-2 rounded-xl'>
                            <RotateCcw />
                        </button>
                    </div>
                </div>
                <div className='flex justify-between items-center p-3 rounded-3xl border'>
                    <div className='border-2 border-dashed p-5 rounded-3xl bg-muted'>
                        <Image src={Iran} alt='iran' className='w-full h-auto' />
                    </div>
                    <div className='grid grid-cols-1 gap-5 w-full px-5 font-light'>
                        <div className='grid grid-cols-3'>
                            <p>
                                <span>
                                    بیمه گذار:
                                </span>
                                <span className='font-bold'>
                                    محمد محمدی
                                </span>
                            </p>
                            <p>
                                <span>
                                    تاریخ خرید: 
                                </span>
                                <span>
                                    1400/01/01
                                </span>
                            </p>
                            <p className='flex gap-2 items-center '>
                                <span>
                                    وضعیت: 
                                </span>
                                <span className='flex gap-2 items-center text-red-600'>
                                 <CircleX size={16} />   لغو شده
                                </span>
                            </p>
                        </div>
                        <div className='grid grid-cols-3'>
                            <p>
                                <span>
                                    نوع بیمه: 
                                </span>
                                <span className='font-bold'>
                                    بیمه شخص ثالث
                                </span>
                            </p>
                            <p>
                                <span>
                                    وسیله: 
                                </span>
                                <span>
                                    پژو پارس
                                </span>
                            </p>
                            <p className='flex gap-1'>
                                <span>
                                    پلاک: 
                                </span>
                                <span>
                                    ایران 25 | 742 الف 45
                                </span>
                            </p>
                        </div>
                    </div>
                    <div className='flex justify-between items-center w-96 bg-gray-50 p-3 py-5 rounded-2xl'>
                        <button className='flex gap-2 items-center bg-disabled text-white p-2 font-light rounded-xl hover:!cursor-not-allowed'>
                        <Eye />    جزئیات بیمه
                        </button>
                        <button className='text-disabled border border-disabled p-2 rounded-xl hover:!cursor-not-allowed'>
                            <FolderDown />
                        </button>
                        <button className='text-disabled border border-disabled p-2 rounded-xl hover:!cursor-not-allowed'>
                            <RotateCcw />
                        </button>
                    </div>
                </div>
            </div>

        </section>
    )
}

export default InsuranceListSection