"use client";

import {
  type ChangeEvent,
  type RefObject,
  type KeyboardEvent,
  useState,
  Dispatch,
  SetStateAction,
} from "react";
import { Accessibility, ChevronDown, X } from "lucide-react";
import PlateCarLeftIcon from "@/components/Common/svg/PlateCarLeftIcon";
import PlateIranIcon from "@/components/Common/svg/PlateIranIcon";
import PlateModalTitleIcon from "@/components/Common/svg/PlateModalTitleIcon";
import {
  Dialog,
  DialogClose,
  DialogContent,    
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/UI/dialog";
import { cn } from "@/lib/utils";


type InputRef = RefObject<HTMLInputElement | null>

interface CarFormProps {
  persianAlphabets: string[];
  selectedAlphabet : string | null
  setSelectedAlphabet: Dispatch<SetStateAction<string | null>>
  input1Ref: InputRef;
  input2Ref: InputRef;
  input3Ref: InputRef;
  selectedOption: string
  nationalCodeRef:InputRef
  selectRef: RefObject<HTMLSelectElement | null>;
  handleInput: (
    currentInput: InputRef,
    nextInput: RefObject<HTMLInputElement | HTMLSelectElement | null>
  ) => (e: ChangeEvent<HTMLInputElement>) => void;
  handleSelectInput: (nextInput: InputRef) => () => void;
  handleKeyDown: (
    currentInput: InputRef,
    previousInput: RefObject<HTMLInputElement | HTMLSelectElement | null>
  ) => (e: KeyboardEvent<HTMLInputElement>) => void;
}

const CarForm = ({ persianAlphabets, selectedAlphabet, setSelectedAlphabet, input1Ref, input2Ref, input3Ref, handleInput, handleKeyDown, selectedOption, nationalCodeRef }: CarFormProps) => {

  const [isOpen, setIsOpen] = useState(false);

  const handleSelect = (item: string) => {
    setSelectedAlphabet(item);
    setIsOpen(false);
    input2Ref.current?.focus()
    // handleSelectInput(input2Ref); // Pass value to parent handler
  };



  return (
    <div className=' mx-auto'>
      <div
        className={cn('w-full overflow-hidden bg-white rounded-lg border-2 h-[63px] border-l-0 border-[#000000] flex items-center justify-between gap-1')}
        style={{ direction: 'ltr' }}
      >
        <div className='w-[10%] flex justify-center bg-[#1D389A] border border-[#1D389A] min-w-[35px] shrink-0 rounded-tl-xl rounded-bl-xl'>
          <PlateCarLeftIcon width={35} height={30} />
        </div>
        <div className='w-[20%] px-0 h-full py-0'>
          <input
            type="tel"
            placeholder="- -"
            ref={input1Ref}
            onInput={(e: ChangeEvent<HTMLInputElement>) => {
              e.target.value = e.target.value.replace(/\D/g, "");
              if (e.target.value.length > e.target.maxLength) {
                e.target.value = e.target.value.slice(0, e.target.maxLength);
              }
              if (e.target.value.length >= e.target.maxLength) {
                e.target.blur()
                setIsOpen(true)
              }
            }}
            maxLength={2}
            className="left-direction !text-xl borderless-input text-center w-full h-full"
            inputMode="numeric"
            style={{ direction: "ltr" }}
          />
        </div>
        <div className='w-[20%]'>
          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
              <div
                role="button"
                tabIndex={0}
                onKeyDown={(e) => (e.key === 'Enter' || e.key === ' ') && setIsOpen(true)}
                className='flex items-center justify-center gap-x-1 cursor-pointer h-full'
              >
                <div className='w-full flex justify-center items-center left-direction !text-xl text-center px-0 h-full py-0'>
                  <span>
                    {selectedAlphabet === "معلولین" ? (
                      <Accessibility size={27} />
                    ) : selectedAlphabet ? (
                      selectedAlphabet
                    ) : (
                      <div className='flex items-center gap-x-1'>
                        <span>-</span>
                        <ChevronDown size={12} className='text-gray-400' />
                      </div>
                    )}
                  </span>
                </div>
              </div>
            </DialogTrigger>

            <DialogContent className='w-fit p-5 bg-[#FFFFFF] [&>button]:hidden borderless-input' onCloseAutoFocus={(e) => e.preventDefault()}>
              <DialogHeader className=''>
                <DialogTitle hidden></DialogTitle>
                <div className='w-full h-full flex items-center justify-between border-b pb-4 border-[#F1F1F1]'>
                  <div className='flex items-center gap-2'>
                    <PlateModalTitleIcon width={20} height={20} />
                    <h3 className='text-[#5E646B] text-sm font-semibold'>انتخاب حرف پلاک</h3>
                  </div>
                  <div className='absolute h-[50px] top-0 left-[10px] w-[40px] rounded-b-full bg-gradient-to-t from-[#F5F6F8] to-transparent'></div>
                  <DialogClose asChild>
                    <div className='absolute top-[15px] left-[14px] h-[30px] w-[30px] rounded-full bg-white cursor-pointer'>
                      <div className='w-full h-full flex items-center justify-center'>
                        <X size={16} />
                      </div>
                    </div>
                  </DialogClose>
                </div>
              </DialogHeader>

              <div className="relative bg-[#FFFFFF] rounded-2xl">
                <div className='flex flex-col gap-2'>
                  <div className='flex flex-wrap w-[300px]'>
                    {persianAlphabets.map((item, index) => (
                      <div className='w-[20%] py-1 flex justify-center items-center' key={index}>
                        <p
                          onClick={() => handleSelect(item)}
                          className={cn("bg-[#F7F8F9] text-sm border font-bold text-gray-600 w-[45px] h-[45px] flex justify-center items-center hover:bg-primary/10 cursor-pointer rounded-xl", {
                            'bg-primary/10 border-primary/40': selectedAlphabet === item
                          })}
                        >
                          {item === "معلولین" ? (
                            <Accessibility className='tex-lg' />
                          ) : (
                            <span>{item}</span>
                          )}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>


        <div className='w-[20%] h-full px-0 py-0'>
          <input
            type="tel"
            placeholder="- - -"
            ref={input2Ref}
            onKeyDown={(e: KeyboardEvent<HTMLInputElement>) => {
              if (
                e.key === "Backspace" &&
                input2Ref.current?.value === ""
              ) {
                setIsOpen(true)
              }
            }}
            maxLength={3}
            onInput={handleInput(input2Ref, input3Ref)}
            className="left-direction borderless-input !text-xl text-center w-full h-full"
            inputMode="numeric"
            style={{ direction: "ltr" }}
          />
        </div>

        <div className="border-l h-full mx-[1%] my-2 border border-[#000000]"></div>

        <div className='flex w-[20%] flex-col items-center gap-1'>
          <PlateIranIcon width={40} height={40} />
          <input
            type="tel"
            placeholder="- -"
            ref={input3Ref}
            onKeyDown={handleKeyDown(input3Ref, input2Ref)}
            maxLength={2}
            onInput={(e: ChangeEvent<HTMLInputElement>) => {
              e.target.value = e.target.value.replace(/\D/g, "");
              if (e.target.value.length >= e.target.maxLength) {
                e.target.value = e.target.value.slice(0, e.target.maxLength);
                if (selectedOption == "withDetails") {
                  nationalCodeRef.current?.focus()
                }
              }
            }}
            className="w-full left-direction borderless-input !text-xl text-center px-0 h-full py-0"
            inputMode="numeric"
            style={{ direction: "ltr" }}
          />
        </div>
      </div>
    </div>
  )
}

export default CarForm