/* Mobile Filter Drawer Styles */
.mobile-filter-drawer {
  /* Smooth backdrop blur effect */
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Custom scrollbar for mobile filter content */
.mobile-filter-content::-webkit-scrollbar {
  width: 4px;
}

.mobile-filter-content::-webkit-scrollbar-track {
  background: transparent;
}

.mobile-filter-content::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.mobile-filter-content::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* Smooth transitions for filter button */
.mobile-filter-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.mobile-filter-button:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Prevent scroll when drawer is open */
.no-scroll {
  overflow: hidden !important;
}

/* Enhanced accordion animations */
.accordion-content {
  transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
  /* Ensure proper touch targets */
  .mobile-filter-drawer button,
  .mobile-filter-drawer input[type="checkbox"],
  .mobile-filter-drawer input[type="radio"] {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Improve text readability */
  .mobile-filter-drawer {
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  /* Better spacing for mobile */
  .mobile-filter-drawer .accordion-item {
    margin-bottom: 1rem;
  }
  
  /* Smooth drawer animation */
  .drawer-enter {
    transform: translateY(100%);
  }
  
  .drawer-enter-active {
    transform: translateY(0);
    transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .drawer-exit {
    transform: translateY(0);
  }
  
  .drawer-exit-active {
    transform: translateY(100%);
    transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }
}
