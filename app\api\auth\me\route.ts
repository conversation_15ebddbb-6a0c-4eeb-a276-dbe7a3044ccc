import { NextResponse } from "next/server";
import { apiClient } from "@/lib/apiClient";

export async function GET() {
  try {
    const res = await apiClient("user", { method: "GET" });
    const data = await res.json();

    return NextResponse.json(data, {
      status: res.status,
    });
  } catch (error) {
    console.error("Failed to fetch user", error);
    return NextResponse.json(
      { error: "Server error" },
      {
        status: 500,
      }
    );
  }
}
