
import {ChevronsLeft} from "lucide-react";
import Link from "next/link";



export default function BlogSectionHead() {
    return (
        <div className='flex flex-col justify-center items-center gap-2'>
            <h2 className='font-light text-[#242021] text-xl md:text-3xl'>بلاگ خودراکس</h2>
            <Link href={"/blog"} className="bg-white border border-gray-200 flex gap-1 w-30 text-[#5E646B] md:mt-2 justify-center rounded-3xl whitespace-nowrap text-xs">
                <span>مشاهده همه</span>
                <ChevronsLeft className="text-[#9DA5B0]"/>
            </Link>
        </div>
    );
}
