"use client";

import { useState } from "react";
import {
  <PERSON>alog,
  <PERSON><PERSON><PERSON>lose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
  DialogFooter,
} from "@/components/UI/dialog";
import CarForm from "@/components/Insurance/PlateForms/CarForm";

export default function SmoothModalDemo() {
  const [modalOpen, setModalOpen] = useState(false);
  const [carFormOpen, setCarFormOpen] = useState(false);
  
  // Mock data for CarForm
  const persianAlphabets = ["الف", "ب", "پ", "ت", "ث", "ج", "چ", "ح", "خ", "د", "ذ", "ر", "ز", "ژ", "س", "ش", "ص", "ض", "ط", "ظ", "ع", "غ", "ف", "ق", "ک", "گ", "ل", "م", "ن", "و", "ه", "ی", "معلولین"];
  const [selected<PERSON><PERSON><PERSON><PERSON>, setSelectedAlphabet] = useState<string | null>(null);
  
  // Mock refs for CarForm
  const input1Ref = { current: null };
  const input2Ref = { current: null };
  const input3Ref = { current: null };
  const nationalCodeRef = { current: null };
  const selectRef = { current: null };
  
  const handleInput = () => () => {};
  const handleSelectInput = () => () => {};
  const handleKeyDown = () => () => {};

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-8">
      <div className="max-w-4xl w-full space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-gray-900">
            Smooth Modal Animations Demo
          </h1>
          <p className="text-lg text-gray-600">
            Test the improved modal animations - both opening and closing should be smooth
          </p>
        </div>

        {/* Demo Cards */}
        <div className="grid md:grid-cols-2 gap-8">
          {/* Basic Modal Demo */}
          <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-200">
            <h2 className="text-2xl font-semibold mb-4 text-gray-800">Basic Modal</h2>
            <p className="text-gray-600 mb-6">
              A simple modal with smooth opening and closing animations
            </p>
            
            <Dialog open={modalOpen} onOpenChange={setModalOpen}>
              <DialogTrigger className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                Open Basic Modal
              </DialogTrigger>
              <DialogContent className="bg-white max-w-md">
                <DialogHeader>
                  <DialogTitle className="text-xl font-semibold text-gray-900">
                    Smooth Animation Test
                  </DialogTitle>
                  <DialogDescription className="text-gray-600">
                    This modal should open and close with smooth, consistent animations.
                    Notice how both the opening and closing transitions are equally smooth.
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4 py-4">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h3 className="font-medium text-blue-900 mb-2">Animation Features:</h3>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• Smooth fade in/out</li>
                      <li>• Scale transition</li>
                      <li>• Consistent timing</li>
                      <li>• Proper state management</li>
                    </ul>
                  </div>
                  
                  <input 
                    type="text" 
                    placeholder="Test input focus..." 
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <DialogFooter className="space-x-2">
                  <DialogClose className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                    Cancel
                  </DialogClose>
                  <button 
                    onClick={() => setModalOpen(false)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    Confirm
                  </button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          {/* Car Form Modal Demo */}
          <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-200">
            <h2 className="text-2xl font-semibold mb-4 text-gray-800">Car Form Modal</h2>
            <p className="text-gray-600 mb-6">
              The actual CarForm component with fixed smooth animations
            </p>
            
            <Dialog open={carFormOpen} onOpenChange={setCarFormOpen}>
              <DialogTrigger className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium">
                Open Car Form
              </DialogTrigger>
              <DialogContent className="bg-white max-w-2xl">
                <DialogHeader>
                  <DialogTitle className="text-xl font-semibold text-gray-900">
                    Car Plate Form
                  </DialogTitle>
                  <DialogDescription className="text-gray-600">
                    Enter your car plate information. The alphabet selection modal now has smooth animations.
                  </DialogDescription>
                </DialogHeader>
                
                <div className="py-6">
                  <CarForm
                    persianAlphabets={persianAlphabets}
                    selectedAlphabet={selectedAlphabet}
                    setSelectedAlphabet={setSelectedAlphabet}
                    input1Ref={input1Ref}
                    input2Ref={input2Ref}
                    input3Ref={input3Ref}
                    selectedOption="withDetails"
                    nationalCodeRef={nationalCodeRef}
                    selectRef={selectRef}
                    handleInput={handleInput}
                    handleSelectInput={handleSelectInput}
                    handleKeyDown={handleKeyDown}
                  />
                </div>

                <DialogFooter className="space-x-2">
                  <DialogClose className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                    Cancel
                  </DialogClose>
                  <button 
                    onClick={() => setCarFormOpen(false)}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                  >
                    Submit
                  </button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-200">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800">Test Instructions</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">What to Test:</h3>
              <ul className="text-gray-600 space-y-1 text-sm">
                <li>• Click to open modals</li>
                <li>• Close using X button</li>
                <li>• Close using Cancel button</li>
                <li>• Close by clicking overlay</li>
                <li>• Close using Escape key</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Expected Behavior:</h3>
              <ul className="text-gray-600 space-y-1 text-sm">
                <li>• Smooth fade in animation</li>
                <li>• Smooth scale transition</li>
                <li>• Consistent timing (300ms)</li>
                <li>• No abrupt disappearing</li>
                <li>• Proper focus management</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}