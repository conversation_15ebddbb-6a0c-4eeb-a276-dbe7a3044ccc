"use client";
import JalaliDatePicker from "@/components/UI/JalaliDatePicker";
import { UserProfileData } from "@/lib/types/types";
import clsx from "clsx";
import { Camera, Check, PencilLine, PlusCircle, Trash2 } from "lucide-react";
import Image from "next/image";
import React, { useState } from "react";
import { Value } from "react-multi-date-picker";
import { useRouter } from "nextjs-toploader/app"; 
// @ts-expect-error: Importing CSS module in a TypeScript file
import "@/components/Insurance/Category/Checkbox.css"
const InsuranceFormDetails = () => {
  const router = useRouter();
  const [selected, setSelected] = useState<string>("");
  const [PlateOwnersSelected, setPlateOwnersSelected] = useState<string>("");
const [images, setImages] = useState<string[]>([]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    debugger;
    const files = e.target.files;
    if (!files) return;

    const newImages: string[] = [];

    Array.from(files).forEach((file) => {
      const previewUrl = URL.createObjectURL(file);
      newImages.push(previewUrl);
    });

    setImages((prev) => [...prev, ...newImages]);
  };

  const handleDelete = (indexToRemove: number) => {
    setImages((prev) => prev.filter((_, i) => i !== indexToRemove));
  };
  const [formData, setFormData] = useState<UserProfileData>({
    full_name: "",
    phone: "",
    email: "",
    national_code: "",
    shamsi_birth_date: "",
    profile_image: "",
  });
  const options = [
    { value: "haqiqi", label: "حقیقی" },
    { value: "hoqouqi", label: "حقوقی" },
  ];
  const plateOwnersOptions = [
    { value: "1", label: "صاحب پلاک همان بیمه‌گزار است" },
    { value: "2", label: "صاحب پلاک شخص دیگری است" },
  ];
  return (
    <div className="md:grid  md:grid-cols-12 gap-4 mb-10 max-md:space-y-5">
      <section className=" col-span-8 ">
        <div className="bg-white p-5 rounded-3xl border">
          <h1>مشخصات بیمه گذار (بیمه شونده)</h1>
          <div>
            <div className="flex flex-col gap-3">
              <label
                htmlFor="insurance"
                className="text-sm font-medium"
              ></label>

              <div className="flex gap-4">
                {options.map((option) => (
                  <label
                    key={option.value}
                    className={`cursor-pointer flex-1 flex items-center justify-between rounded-2xl border p-4 transition
                                            ${
                                              selected === option.value
                                                ? "border-green-500 bg-green-50 text-green-600"
                                                : "border-gray-300 bg-gray-50 text-gray-500"
                                            }`}
                  >
                    <span className="text-sm">{option.label}</span>
                    <input
                      type="radio"
                      name="replacement"
                      value={option.value}
                      checked={selected === option.value}
                      onChange={() => setSelected(option.value)}
                      className="hidden"
                    />
                    {selected === option.value ? (
                      <Check className="w-5 h-5 text-green-500" />
                    ) : (
                      <span className="w-5 h-5 rounded-full border border-gray-300"></span>
                    )}
                  </label>
                ))}
              </div>
            </div>
            <div className="bg-[#fff5d8b1] p-2 mt-10 border-2 border-[#f7bb0670] border-dashed max-md:text-sm leading-8 rounded-xl">
              <p className="">
                کد ملی و شماره تلفن بیمه گزار باید متعلق به یک فرد باشد.
              </p>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-8">
              <div className="flex flex-col gap-2">
                <label htmlFor="firstName" className="text-sm font-medium">
                  نام*
                </label>
                <input
                  type="text"
                  id="firstName"
                  className="border bg-gray-50 border-gray-300 p-3 rounded-2xl"
                />
              </div>
              <div className="flex flex-col gap-2">
                <label htmlFor="lastName" className="text-sm font-medium">
                  نام خانوادگی*
                </label>
                <input
                  type="text"
                  id="lastName"
                  className="border bg-gray-50 border-gray-300 p-3 rounded-2xl"
                />
              </div>
              <div className="flex flex-col gap-3 col-span-2">
                <label htmlFor="shamsi_birth_date">تاریخ تولد</label>
                <JalaliDatePicker
                  value={formData.shamsi_birth_date || ""}
                  setValue={(val: Value) =>
                    setFormData((prev) => ({
                      ...prev,
                      shamsi_birth_date: val?.toString?.() ?? "",
                    }))
                  }
                  // disabled={isSubmitting}
                />
              </div>
              <div className="flex flex-col gap-2">
                <label htmlFor="nationalId" className="text-sm font-medium">
                  کد ملی*
                </label>
                <input
                  type="text"
                  id="nationalId"
                  className="border bg-gray-50 border-gray-300 p-3 rounded-2xl"
                />
              </div>
              <div className="flex flex-col gap-2">
                <label htmlFor="phone" className="text-sm font-medium">
                  تلفن همراه*
                </label>
                <input
                  type="text"
                  id="phone"
                  className="border bg-gray-50 border-gray-300 p-3 rounded-2xl"
                />
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white p-5 rounded-3xl mt-5 border">
          <h2>مشخصات صاحب پلاک</h2>
          <div className="flex flex-col gap-3">
            <label htmlFor="insurance" className="text-sm font-medium"></label>

            <div className="md:flex gap-4">
              {plateOwnersOptions.map((option) => (
                <label
                  key={option.value}
                  className={`cursor-pointer max-md:mt-5 flex-1 flex items-center justify-between rounded-2xl border p-4 transition
                                            ${
                                              PlateOwnersSelected ===
                                              option.value
                                                ? "!border-green-500 bg-green-50 !text-green-600"
                                                : "border-gray-300 bg-gray-50 text-gray-500"
                                            }`}
                >
                  <span className="text-sm">{option.label}</span>
                  <input
                    type="radio"
                    name="replacement"
                    value={option.value}
                    checked={selected === option.value}
                    onChange={() => setPlateOwnersSelected(option.value)}
                    className="hidden"
                  />
                  {PlateOwnersSelected === option.value ? (
                    <Check className="w-5 h-5 text-green-500" />
                  ) : (
                    <span className="w-5 h-5 rounded-full border border-gray-300"></span>
                  )}
                </label>
              ))}
            </div>
          </div>
        </div>
        <div className="bg-white p-5 rounded-3xl mt-5 border">
          <div className="flex justify-between max-md:mb-5">
            <h2>آدرس مورد نظر جهت درج در بیمه نامه را وارد نمایید</h2>
            <button className="max-md:hidden">افزودن آدرس جدید</button>
          </div>
          <div>
            <div
            //   onClick={handleClick}
              className={clsx(
                "mt-3 text-gray-400 p-3 max-md:py-2 rounded-3xl border cursor-pointer transition-all duration-300",
                // selectable &&
                //   (isChecked
                //     ? "border-[#3c53c7] shadow-sm scale-[1.01] bg-white"
                //     : "border-gray-200 hover:border-[#3c53c7]")
              )}
            //   data-id={id}
            //   data-selectable={selectable ? "true" : "false"}
            //   data-selected={isChecked ? "true" : "false"}
            >
              <div className="payment-method-item flex justify-between items-center max-md:text-sm p-3">
                <div className="flex md:gap-5 max-md:gap-2 items-center">
                  {/* <div className=" md:px-4 md:py-1 rounded-2xl">
                            <Image className='' src={image} alt='' />
                          </div> */}
                  <div className="flex flex-col gap-3 max-md:gap-1">
                    <span className="text-primary text-sm max-md:text-sm">
                        <p>
                          {" "}
                          گیرنده: {"محمد"} - {"09103523456"}
                        </p>
                     
                    </span>
                    <div className="text-sm font-extralight">
                      <p className="flex flex-col max-md:text-sm !leading-6">
                        {
                          `${"اصفهان"} ${ "اصفهان"} ${
                              "چمران کوچه 10"
                            }`
                        }

                          <span className="text-sm max-md:text-xs mt-2 max-md:mt-1 flex items-center gap-2">
                            <PencilLine size={18} /> ویرایش آدرس
                          </span>
                        
                      </p>
                    </div>
                  </div>
                </div>
                <div>
                  <div className="checkbox-wrapper-15">
                    <input
                    //   ref={checkboxRef}
                      className="inp-cbx"
                      id={`cbx-${1}`}
                      type="checkbox"
                      style={{ display: "none" }}
                    />
                    <label className="cbx" htmlFor={`cbx-${1}`}>
                      <span>
                        <svg width="12px" height="9px" viewBox="0 0 12 9">
                          <polyline points="1 5 4 8 11 1"></polyline>
                        </svg>
                      </span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex justify-center items-center mt-5">
              <button className="flex items-center gap-2 border px-10 py-4 text-muted-foreground  rounded-2xl text-sm">
               <PlusCircle size={20} /> افزودن آدرس جدید
              </button>
            </div>
          </div>
        </div>
      </section>
      <section className="col-span-4">
        <div className="bg-white rounded-2xl p-5 flex flex-col gap-3 border border-gray-200">
          <div className=" mt-3">
            <h2 className="title-border-bottom pb-3 relative w-fit">
              بارگذاری مدارک بیمه‌نامه
            </h2>
          </div>
          <div className="group relative col-span-2">
            <input
              type="file"
              id="profilePhoto"
              accept=".jpg,.jpeg,.png"
              className="hidden"
              // onChange={handleFileChange}
              // disabled={isSubmitting}
            />
            <div className="flex flex-col gap-3">
              <p className="px-1">عکس کارت ماشین یا برگ سبز</p>
              <label
                htmlFor="profilePhoto"
                className={`flex bg-gray-50 justify-center items-center gap-5 border-2 border-dashed border-gray-300 rounded-2xl p-6 py-3 cursor-pointer hover:border-primary transition-colors `} // ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}
              >
                <div className="p-2 border-gray-300 rounded-full hover:border-primary transition-colors">
                  <div className="w-12 h-12 bg-gray-400 flex items-center justify-center overflow-hidden rounded-full relative">
                    {/* {profileImageFile || formData.profile_image ? (
                                        <Image
                                            src={ProfilePicture}
                                            alt="preview"
                                            fill
                                            className="w-full h-full object-cover"
                                        />
                                    ) : ( */}
                    <Camera stroke="white" size={30} />
                    {/* )} */}
                  </div>
                  <p className="mt-3 text-xs">آپلود عکس</p>
                </div>
              </label>
            </div>
            <div className="flex flex-col gap-3 my-5">
              <p className="px-1">عکس پشت کارت ماشین یا برگ سبز</p>

              {/* دکمه آپلود */}
              <label
                htmlFor="galleryUploader"
                className="flex bg-gray-50 justify-center items-center gap-5 border-2 border-dashed border-gray-300 rounded-2xl p-6 py-3 cursor-pointer hover:border-primary transition-colors"
              >
                <div className="p-2 border-gray-300 rounded-full group-hover:border-primary transition-colors">
                  <div className="w-12 h-12 bg-gray-400 flex items-center justify-center overflow-hidden rounded-full relative">
                    <Camera stroke="white" size={30} />
                  </div>
                  <p className="mt-3 text-xs text-center">آپلود عکس</p>
                </div>
                <input
                  id="galleryUploader"
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleFileChange}
                  className="hidden"
                />
              </label>

              <div className="flex gap-2 flex-wrap gap-y-3 mt-4">
                {images.map((imgSrc, index) => (
                  <div
                    key={index}
                    className="relative group w-[30%] h-[58px] rounded-lg overflow-hidden"
                  >
                    <Image
                      src={imgSrc}
                      alt={`uploaded image ${index}`}
                      fill
                      className="object-cover"
                    />

                    <div className="absolute inset-0 bg-black/40 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                      <button
                        // onClick={() => handleDelete(index)}
                        className="text-white cursor-pointer bg-red-600 hover:bg-red-700 p-1 rounded-full"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="flex flex-col gap-3">
              <p className="px-1">عکس بیمه‌نامه</p>
              <label
                htmlFor="profilePhoto"
                className={`flex bg-gray-50 justify-center items-center gap-5 border-2 border-dashed border-gray-300 rounded-2xl p-6 py-3 cursor-pointer hover:border-primary transition-colors `} // ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}
              >
                <div className="p-2 border-gray-300 rounded-full hover:border-primary transition-colors">
                  <div className="w-12 h-12 bg-gray-400 flex items-center justify-center overflow-hidden rounded-full relative">
                    {/* {profileImageFile || formData.profile_image ? (
                                        <Image
                                            src={ProfilePicture}
                                            alt="preview"
                                            fill
                                            className="w-full h-full object-cover"
                                        />
                                    ) : ( */}
                    <Camera stroke="white" size={30} />
                    {/* )} */}
                  </div>
                  <p className="mt-3 text-xs">آپلود عکس</p>
                </div>
              </label>
            </div>
          </div>
          <div className="flex flex-col gap-3 mt-5">
            <button onClick={() => router.push("/bime-form?step=2")} className="bg-primary text-white rounded-3xl p-3 py-5">
              تایید و ادامه
            </button>
            <button className="border border-gray-300 rounded-3xl p-3 py-5">
              مرحله قبل
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default InsuranceFormDetails;
