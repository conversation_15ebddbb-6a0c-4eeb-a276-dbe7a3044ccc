"use client"

import { Check, ChevronDown } from "lucide-react"
import { useState } from "react";
import { useRouter } from 'nextjs-toploader/app'

const InsuranceStatusStep = () => {
    const router = useRouter()
    const [selected, setSelected] = useState<string>("");



    const options = [
        { value: "yes", label: "بله تعویض پلاک داشته است" },
        { value: "no", label: "خیر تعویض پلاک نداشته است" },
    ];
    return (
        <div className="flex flex-col gap-8">
            <div className="flex flex-col gap-3">
                <label htmlFor="insurance" className="text-sm font-medium">
                    آیا خودروی شما بیمه‌نامه شخص ثالث دارد؟
                </label>

                <div className="relative w-full">
                    <select

                        id="insurance"
                        className="appearance-none w-full h-12 border bg-gray-50 border-gray-300 rounded-xl px-3 pr-4 text-sm leading-none 
                 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-none"
                    >
                        <option value="">انتخاب کنید</option>
                        <option value="true">بله</option>
                        <option value="false">خیر</option>
                    </select>


                    <div className="pointer-events-none absolute inset-y-0 left-3 flex items-center">
                        <ChevronDown className="w-4 h-4 text-gray-500" />
                    </div>
                </div>
            </div>

            <div className="flex flex-col gap-3">
                <label htmlFor="insurance" className="text-sm font-medium">
                    آیا خودرو شما در مدت بیمه نامه قبلی تعویض پلاک (تغییر مالکیت) داشته است؟
                </label>

                <div className="flex gap-4">
                    {options.map((option) => (
                        <label
                            key={option.value}
                            className={`cursor-pointer flex-1 flex items-center justify-between rounded-2xl border p-4 transition
                            ${selected === option.value
                                    ? "border-green-500 bg-green-50 text-green-600"
                                    : "border-gray-300 bg-gray-50 text-gray-500"
                                }`}
                        >
                            <span className="text-sm">{option.label}</span>
                            <input
                                type="radio"
                                name="replacement"
                                value={option.value}
                                checked={selected === option.value}
                                onChange={() => setSelected(option.value)}
                                className="hidden"
                            />
                            {selected === option.value ? (
                                <Check className="w-5 h-5 text-green-500" />
                            ) : (
                                <span className="w-5 h-5 rounded-full border border-gray-300"></span>
                            )}
                        </label>
                    ))}
                </div>
            </div>
            <div className="bg-[#fff5d8b1] p-2 border-2 border-[#f7bb0670] border-dashed max-md:text-sm leading-8 rounded-xl"
                 style={{
                        backgroundImage: "url('/assets/images/alert-triangl.png')",
                        backgroundRepeat: "no-repeat",
                        backgroundPosition: "25px center",
                    }}
            >
                <p className="mb-10">
                    طبق بخشنامه بیمه مرکزی، در صورت تغییر مالکیت در مدت بیمه‌نامه قبلی تنها در شرایط زیر امکان انتقال تخفیفات بیمه‌نامه وجود دارد                   
                </p>
                <p>
                     مالک جدید، الحاقیه انتقال تخفیف داشته باشد.
                     <br />
                    تغییر مالکیت بین بستگان درجه اول (پدر، مادر، همسر و فرزند) رخ داده باشد.
                </p>
            </div>
             <div className='flex justify-between gap-10'>
                <button onClick={() => router.push("/bime-car-info?step=1")} className='w-1/2 border rounded-xl p-3'>
                    مرحله قبل
                </button>
                <button onClick={() => router.push("/bime-car-info?step=3")} className='w-1/2 bg-primary text-white rounded-xl p-3'>
                    تایید و ادامه
                </button>
            </div>

        </div>
    )
}

export default InsuranceStatusStep