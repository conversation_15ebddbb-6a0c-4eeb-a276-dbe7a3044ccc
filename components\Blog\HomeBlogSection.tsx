import BlogSectionHead from "./BlogSectionHead";
import BlogSlider from "./BlogSlider";
import { ArticlesApiResponse } from "@/lib/types/articles-types"; 
import { apiClient } from "@/lib/apiClient";

export default async function HomeBlogSection() {

     const response:ArticlesApiResponse = await apiClient("articles")
      .then(res => res.json())
    

    // if (!response.success) {
    //     console.error(response.message);
    //     return;
    // }

    const blogs = response?.data ?? []

    return (
        <div className="w-full max-w-screen-xl mx-auto px-2">
            <div className='flex flex-col gap-y-5 md:gap-y-8'>
                <BlogSectionHead/>
                {blogs.length === 0 &&
                    <div className='w-full h-[150px] rounded-2xl flex justify-center items-center bg-white'>
                        <p>در حال حاضر هیچ پستی در بلاگ موجود نیست</p>
                    </div>}
                {blogs.length > 0 && <BlogSlider blogPosts={blogs}/>}
            </div>
        </div>
    );
}
