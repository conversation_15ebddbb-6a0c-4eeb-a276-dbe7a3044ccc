"use client";

import * as Dialog from "@radix-ui/react-dialog";
import { useState, useEffect, useRef } from "react";
import { Loader, X } from "lucide-react";
import { sendLoginCode, verifyCode } from "@/actions/login.action";
import { useRouter } from "nextjs-toploader/app";

export default function LoginModal({
    open,
    onOpenChange,
}: {
    open: boolean;
    onOpenChange: (v: boolean) => void;
}) {
    const [step, setStep] = useState<1 | 2>(1);
    const [phone, setPhone] = useState("");
    const [code, setCode] = useState(["", "", "", "", ""]);
    const [timer, setTimer] = useState(180);
    const [isSending, setIsSending] = useState(false);
    const [isCodeSent, setIsCodeSent] = useState(false);
    const [phoneError, setPhoneError] = useState(false);
    const [token, setToken] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const router = useRouter()

    const codeRefs = useRef<HTMLInputElement[]>([]);

    useEffect(() => {
        let interval: NodeJS.Timeout | null = null;

        if (step === 2 && timer > 0) {
            interval = setInterval(() => setTimer((t) => t - 1), 1000);
        }

        return () => {
            if (interval) clearInterval(interval);
        };
    }, [step, timer]);


    const formattedTimer = () => {
        const minutes = Math.floor(timer / 60);
        const seconds = timer % 60;
        return `${minutes}:${seconds < 10 ? "0" + seconds : seconds}`;
    };
        const handleSendCode = async () => {
        if (!phone || phone.length < 11) {
            setPhoneError(true);
        } else {
            setPhoneError(false);
            setIsSending(true); // Show the spinner


            const response = await sendLoginCode(phone);


            if (response?.success) {
                setToken(response?.data.token);
                setIsCodeSent(true);
                 console.log("success");
                     setStep(2);
                     setTimer(180);
                     setTimeout(() => codeRefs.current[0]?.focus(), 100);

                
                setIsSending(false);
            } else {
                setIsSending(false)
                if (response?.data?.message) {
                    // showToast(response?.data?.message)
                   
                } else {
                    console.log("error");
                    // showToast("مشکلی پیش آمده. لطفا بعد از چند دقیقه دوباره تلاش کنید")
                }
            }

        }
    };

    const handleNext = () => {
       
       
        
        handleSendCode()
    };


    const handleResend = () => {
        setTimer(180);
        // TODO: call resend API
        handleSendCode()
    };

    const handleCodeChange = (value: string, i: number) => {
        if (!/^[0-9]*$/.test(value)) return;

        const newCode = [...code];
        newCode[i] = value;
        setCode(newCode);

        if (value && i < 4) {
            codeRefs.current[i + 1]?.focus();
        }
    };
        const handleVerifyCode = async () => {
        if (!code || !token) return;
        setIsLoading(true)
        debugger
        const response = await verifyCode(phone, code.join(""), token);
        if (!response.success || response.status == 400) {
            setIsLoading(false)
            return setTimeout(() => {
                console.log(response?.data?.message || "کد وارد شده یا اطلاعات ورودی اشتباه است")
            }, 500);
        }
        if (response?.success) {
            setIsLoading(false)
            onOpenChange(false)
            // router.push("/dashboard")
        }

        // if (response?.success) {
        //     // Update the context with fresh user data
        //     await updateData();
        // }

        
    };

    const handleClose = () => {
        onOpenChange(false)
    };

    return (
        <Dialog.Root open={open} onOpenChange={onOpenChange}>
            <Dialog.Portal>
                <Dialog.Overlay className="fixed inset-0 bg-black/40 backdrop-blur-sm z-40" />

                <Dialog.Content
                    className="
                        fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
                        z-50 w-[90%] max-w-[420px]
                        bg-white rounded-3xl shadow-xl p-6
                        animate-in zoom-in fade-in
                    "
                >
                    <Dialog.Title className="sr-only">Login Modal</Dialog.Title>

                    {/* Header */}
                    <div className="flex justify-between items-center">
                        <h2 className="text-lg font-semibold">
                            {step === 1 ? "ورود / ثبت نام" : "تایید کد ارسال‌شده"}
                        </h2>
                        <button onClick={handleClose}>
                            <X size={22} />
                        </button>
                    </div>

                    {/* STEP 1 */}
                    {step === 1 && (
                        <div className="space-y-4 mt-4">
                            <p className="text-sm font-light">
                                شماره موبایل خود را وارد کنید
                            </p>

                            <input
                                maxLength={11}
                                value={phone}
                                onChange={(e) => setPhone(e.target.value)}
                                className="
                                    w-full p-3 border border-gray-300 rounded-xl
                                    focus:ring-2 focus:ring-primary outline-none
                                "
                                placeholder="مثلاً 09123456789"
                            />

                            <button
                                onClick={handleNext}
                                disabled={phone.length !== 11 || isSending}
                                className="
                                    w-full bg-primary text-white text-center flex justify-center p-3 rounded-xl
                                    
                                "
                            >
                            {
                                isSending ? (
                                    <Loader className="rounded-full animate-spin text-center"/>
                                ) : (
                                    "دریافت کد تایید"
                                )
                            }
                                
                            </button>
                        </div>
                    )}

                    {/* STEP 2 */}
                    {step === 2 && (
                        <div className="space-y-5 mt-4">
                            <p className="text-sm text-center font-light">
                                کد ۵ رقمی ارسال‌شده به{" "}
                                <span className="font-semibold">{phone}</span>{" "}
                                را وارد کنید
                            </p>


                            <div className="flex justify-center gap-3" dir="ltr">
                                {code.map((digit, i) => (
                                    <input
                                        key={i}
                                        ref={(el) => {
                                            if (el) codeRefs.current[i] = el;
                                        }}
                                        maxLength={1}
                                        value={digit}
                                        onChange={(e) =>
                                            handleCodeChange(e.target.value, i)
                                        }
                                        inputMode="numeric"
                                        dir="ltr"
                                        style={{
                                            direction: "ltr",
                                            unicodeBidi: "plaintext",
                                        }}
                                        onKeyDown={(e) => {
                                            if (e.key === "Backspace") {
                                                if (digit === "") {
                                                    if (i > 0) {
                                                        codeRefs.current[
                                                            i - 1
                                                        ]?.focus();
                                                        const newCode = [...code];
                                                        newCode[i - 1] = "";
                                                        setCode(newCode);
                                                    }
                                                } else {
                                                    const newCode = [...code];
                                                    newCode[i] = "";
                                                    setCode(newCode);
                                                }
                                            }
                                        }}
                                        className="
                                            w-12 h-14 text-center text-xl
                                            border border-gray-300 rounded-xl
                                            focus:ring-2 focus:ring-primary outline-none
                                        "
                                    />
                                ))}
                            </div>

                            {timer > 0 ? (
                                <p className="text-center text-gray-600">
                                    ارسال مجدد کد تا{" "}
                                    <span className="font-bold">
                                        {formattedTimer()}
                                    </span>
                                </p>
                            ) : (
                                <button
                                    className="text-primary w-full text-center"
                                    onClick={handleResend}
                                >
                                    ارسال مجدد کد
                                </button>
                            )}

                            <button
                                onClick={() => setStep(1)}
                                className="text-sm underline text-gray-700 w-full text-center"
                            >
                                تغییر شماره موبایل
                            </button>

                            <button
                                onClick={handleVerifyCode}
                                className="w-full bg-primary text-white p-3 rounded-xl"
                            >
                                تایید و ورود
                            </button>
                        </div>
                    )}
                </Dialog.Content>
            </Dialog.Portal>
        </Dialog.Root>
    );
}
