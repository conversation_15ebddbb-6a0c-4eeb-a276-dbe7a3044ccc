"use client"
import { useState } from "react";
import Sortbar from "@/components/Insurance/Category/Sortbar";
import SpecialOffer from "@/public/assets/images/special-offer.webp"
import Image from "next/image";
import Iran from "@/public/assets/images/IRAN.webp"
import Dana from "@/public/assets/images/dana.webp"
import Moalem from "@/public/assets/images/MOALLEM.webp"
import Novin from "@/public/assets/images/novin.webp"
// import CardCheckIcon from "@/components/common/svg/CardCheckIcon";
import { Car } from "lucide-react";
import InsuranceItem from "@/components/Insurance/Category/InsuranceItem";
import AccordionHeader from "@/components/UI/AccordionHeader";
import InsurancePriceFilter from "@/components/Insurance/Category/InsurancePriceFilter";
import Checkbox from "@/components/Insurance/Category/Checkbox";
import MobileFilterDrawer from "@/components/Insurance/Category/MobileFilterDrawer";
import MobileFilterButton from "@/components/Insurance/Category/MobileFilterButton";
import FilterSection from "@/components/Insurance/Category/FilterSection";

const CategoryContainer = () => {
    const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);
    const [activeFiltersCount, setActiveFiltersCount] = useState(0);
    const [sortValue, setSortValue] = useState("all");

    const handleApplyFilters = () => {
        // Handle filter application logic here
        console.log("Filters applied");
    };

    const handleResetFilters = () => {
        setActiveFiltersCount(0);
        // Handle filter reset logic here
        console.log("Filters reset");
    };

    const handleSortChange = (value: string) => {
        setSortValue(value);
        console.log("Sort changed to:", value);
    };
    return (
        <div className="md:grid max-md:px-3 grid-cols-12 gap-10 min-h-screen mt-10 max-w-7xl mx-auto">
            {/* Desktop Filters Sidebar */}
            <FilterSection className="col-span-3">
                <div className="bg-white border rounded-3xl p-5 py-7 search-products-filter flex flex-col gap-5"
                    style={{
                        backgroundImage: "url('/assets/images/half-circle-yellow.png')",
                        backgroundRepeat: "no-repeat",
                    }}
                >
                    <h3 className="relative pb-3">
                        بیمه شخص ثالث خودرو
                    </h3>
                    <div className="flex gap-3 items-center">
                        <Car size={40} color="#596068" />
                        <div className="text-sm font-light space-y-1">
                            <p>
                                <span className="text-sm font-light">
                                    پژو پارس سال
                                </span>
                                <span>مدل:  / 1394</span>
                            </p>
                            <p>
                                <span className="text-sm font-light">
                                    تاریخ اتمام بیمه:
                                </span>
                                <span>12  / 1394</span>
                            </p>

                        </div>
                    </div>
                    <div>
                        <button className="font-light w-full bg-gray-100/85 rounded-xl p-3 border-gray-200 border"> ویرایش اطلاعات خودرو</button>
                    </div>
                </div>
                <div className="bg-white border mt-4 max-md:border max-md:mb-3 border-gray-200 min-h-20 rounded-3xl p-3 search-products-filter">
                    <AccordionHeader title="کد تخفیف دارم">
                        <div>
                            <input type="text" className="w-full bg-[#F9FAFB] rounded-3xl p-3 border-gray-200 border text-gray-300 font-light" placeholder="کد تخفیف را وارد کنید" />
                        </div>
                    </AccordionHeader>
                </div>
                <div
                    className='bg-white border max-md:border max-md:mb-3 mt-5 border-gray-200 min-h-20 rounded-3xl p-3 search-products-filter'>
                    <AccordionHeader title='محدوده قیمت'>
                        <InsurancePriceFilter min={0} max={1000000000} minRange={10000000} maxRange={700000000} />
                    </AccordionHeader>
                </div>
                <div className="bg-white border mt-4 max-md:border max-md:mb-3 border-gray-200 min-h-20 rounded-3xl p-3 search-products-filter">
                    <AccordionHeader title="مدت اعتبار بیمه نامه">
                        <ul className="flex flex-col gap-3 font-light">
                            <li>
                                <Checkbox
                                    id="cbx-15"
                                    label="6 ماهه"
                                // checked={true}
                                // onChange={(e) => {}}
                                />


                            </li>
                            <li>
                                <Checkbox
                                    id="cbx-1"
                                    label="1 ساله"
                                // checked={true}
                                // onChange={(e) => {}}
                                />


                            </li>
                            <li>
                                <Checkbox
                                    id="cbx-13"
                                    label="3 ماهه"
                                // checked={true}
                                // onChange={(e) => {}}
                                />


                            </li>
                            <li>
                                <Checkbox
                                    id="cbx-14"
                                    label="1 ماهه"
                                // checked={true}
                                // onChange={(e) => {}}
                                />


                            </li>
                            <li>
                                <Checkbox
                                    id="cbx-9"
                                    label="9 ماهه"
                                // checked={true}
                                // onChange={(e) => {}}
                                />


                            </li>
                        </ul>
                    </AccordionHeader>
                </div>
                <div
                    className='bg-white border max-md:border max-md:mb-3 border-gray-200 min-h-20 rounded-3xl p-3 search-products-filter flex justify-between mt-5'>
                    <label className="flex w-full items-center cursor-pointer justify-between">
                        <span className="ms-3 text-sm font-medium  text-gray-900 dark:text-gray-300"> امکان پرداخت قسطی </span>
                        <input type="checkbox"
                            //    value={value}
                            //    checked={value === 'true'}
                            //    onChange={handleChange}
                            className="sr-only peer" />
                        <div
                            className="relative w-11 h-6 bg-gray-200  rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600 "></div>

                    </label>
                </div>
                <div className="bg-white border mt-4 max-md:border max-md:mb-3 border-gray-200 min-h-20 rounded-3xl p-3 search-products-filter">
                    <AccordionHeader title="شرکت بیمه" >
                        <ul className="pb-5 flex flex-col gap-3">
                            <label htmlFor="iran" className="w-full flex justify-between items-center">
                                <li className="flex justify-between items-center w-full">
                                    <div className="flex gap-3 items-center text-sm">
                                        <Image width={20} height={10} src={Iran} alt='iran' />
                                        <span>بیمه ایران</span>
                                    </div>
                                    <div>
                                        <input type="checkbox" className="" id="iran" />
                                    </div>
                                </li>

                            </label>
                            <label htmlFor="dana" className="w-full flex justify-between items-center">
                                <li className="flex justify-between items-center w-full">
                                    <div className="flex gap-3 items-center text-sm">
                                        <Image width={20} height={10} src={Dana} alt='dana' />
                                        <span>بیمه دانا</span>
                                    </div>
                                    <div>
                                        <input type="checkbox" className="" id="dana" />
                                    </div>
                                </li>
                            </label>
                            <label htmlFor="moalem" className="w-full flex justify-between items-center">
                                <li className="flex justify-between items-center w-full">
                                    <div className="flex gap-3 items-center text-sm">
                                        <Image width={20} height={10} src={Moalem} alt='moalem' />
                                        <span>بیمه معلوم</span>
                                    </div>
                                    <div>
                                        <input type="checkbox" className="" id="moalem" />
                                    </div>
                                </li>
                            </label>
                            <label htmlFor="novin" className="w-full flex justify-between items-center">
                                <li className="flex justify-between items-center w-full">
                                    <div className="flex gap-3 items-center text-sm">
                                        <Image width={20} height={10} src={Novin} alt='novin' />
                                        <span>بیمه نوین</span>
                                    </div>
                                    <div>
                                        <input type="checkbox" className="" id="novin" />
                                    </div>
                                </li>
                            </label>
                        </ul>
                    </AccordionHeader>
                </div>

            </FilterSection>
            <div className="col-span-9">
                <section className="mb-5">
                    <Sortbar onSortChange={handleSortChange} resultCount={31} />
                </section>
                <section className="bg-gradient-to-l from-[#3B38FD] to-[#1F84FB] rounded-3xl flex max-md:flex-wrap md:items-center md:justify-between p-5 max-md:px-3">
                    <div className="flex items-center gap-3">
                        <Image src={SpecialOffer} alt='special-offer' />
                        <p className="text-white font-light">
                            تا ۹٪ تخفیف نقدی بیمه شخص ثالث با کد dffdaq
                            <div className="md:hidden">
                                <button className="bg-warning font-medium text-gray-700 px-5 max-md:text-sm py-2 rounded-lg">
                                    اعمال کد تخفیف
                                </button>
                            </div>
                        </p>
                    </div>
                    <div>
                        <button className="bg-warning max-md:hidden text-gray-700 px-5 max-md:text-sm py-2 rounded-lg">
                            اعمال کد تخفیف
                        </button>
                    </div>
                </section>
                <section className="mt-5 flex flex-col gap-5">
                    {/* <div className="flex justify-between bg-white p-5 rounded-3xl border">
                        <div className="w-[15%] relative p-5 border-2 border-dashed rounded-2xl">
                            <Image src={Moalem} alt='iran' className="w-full" width={300} height={300} />
                        </div>
                        <div className="w-[50%]">
                            <div className="flex pb-3 justify-between items-center">
                                <h2 className="title-border relative">
                                    بیمه ایران
                                </h2>
                                <div className="flex items-center gap-3 text-sm font-light">
                                    <span className="bg-[#E0F3E6] px-2 py-1 text-[#2DC058] rounded-3xl">
                                        امکان خرید قسطی
                                    </span>
                                    <span className="bg-[#FFF7DF] px-2 py-1 text-[#F7BC06] rounded-3xl">
                                        بدون پیش پرداخت
                                    </span>
                                </div>
                            </div>
                            <div className="font-light mt-3">
                                <p className="flex items-center gap-2">
                                    <CreditCard color="#9da5b0" size={18} />   امکان پرداخت خسارت سیار (تمامی شهر ها)
                                </p>
                                <div className="flex gap-5 mt-3">
                                    <p className="flex items-center gap-2">
                                        <Pin color="#9da5b0" size={18} />  <span className="font-bold">58</span> شعبه
                                    </p>
                                    <p className="flex items-center gap-2">
                                        <Star size={18} fill='#F7BC06' color='#F7BC06' />  3 از 5 تعهد در پرداخت
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="w-[28%] bg-gray-50 rounded-3xl p-3 flex justify-center items-center flex-col gap-3">
                            <div className="flex gap-1 items-center">
                                <SpecialPrice price={798545646} className="text-sm" />
                                <span className="bg-red-500 text-white p-[2px] text-xs rounded-bl-full rounded-tl-full rounded-tr-full ">10%</span>
                            </div>
                            <div className="flex justify-between w-full items-center">
                                <span>مبلغ نهایی: </span>
                                <p>
                                    <span>8,029,000</span> تومان
                                </p>
                            </div>
                            <button className="w-full bg-primary text-white p-3 py-2 rounded-xl  font-light">خرید بیمه</button>
                        </div>
                    </div> */}
                    <InsuranceItem />
                    <InsuranceItem />
                    <InsuranceItem />
                    <InsuranceItem />
                    <InsuranceItem />
                    <InsuranceItem />
                    <InsuranceItem />
                    <InsuranceItem />
                </section>
            </div>

            {/* Mobile Filter Components */}
            <MobileFilterButton
                onClick={() => setIsMobileFilterOpen(true)}
                activeFiltersCount={activeFiltersCount}
            />

            <MobileFilterDrawer
                isOpen={isMobileFilterOpen}
                onClose={() => setIsMobileFilterOpen(false)}
                onApplyFilters={handleApplyFilters}
                onResetFilters={handleResetFilters}
            />
        </div>
    )
}

export default CategoryContainer