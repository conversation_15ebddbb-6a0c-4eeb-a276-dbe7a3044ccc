'use client';

import { Check, Loader2 } from 'lucide-react';
import React from 'react';
import clsx from 'clsx';

type Step = {
  title: string;
  status: 'completed' | 'current' | 'upcoming';
};

interface CheckoutProgressProps {
  steps: Step[];
}

export default function CheckoutProgress({ steps }: CheckoutProgressProps) {
  const getConnectorClass = (status: Step['status'], prevStatus?: Step['status']) =>
    clsx(
      'max-md:w-[34%] w-[40%] h-1', // shrink 0 removed from this line
      status === 'completed' && 'bg-blue-400',
      status === 'current' && (prevStatus === 'completed' ? 'bg-gradient-to-l from-primary to-green-500' : 'bg-green-500'),
      status === 'upcoming' && 'bg-gray-300'
    );

  const getStepCircleClass = (status: Step['status']) =>
    clsx(
      'max-md:w-7 max-md:h-7 w-8 h-8 rounded-full flex items-center justify-center relative',
      status === 'completed' && 'bg-blue-400',
      status === 'current' && 'bg-green-400',
      status === 'upcoming' && 'bg-white border-4 border-gray-300'
    );

  const getStepContent = (step: Step) => {
    switch (step.status) {
      case 'completed':
        return {
          icon: <Check stroke='white' size={20} />,
          textClass: 'text-blue-400',
        };
      case 'current':
        return {
          icon: <Loader2 size={20} stroke='white' className="animate-spin" />,
          textClass: 'text-green-400 font-bold',
        };
      default:
        return {
          icon: null,
          textClass: 'text-gray-400',
        };
    }
  };

  return (
    <div className='mb-5 md:px-12 p-12 max-md:p-7 max-md:py-10 bg-white rounded-3xl max-md:rounded-2xl'>
      <div className='w-full flex justify-center max-md:justify-between items-center gap-0'>
        {steps.map((step, index) => {
          const prevStep = steps[index - 1];
          const { icon, textClass } = getStepContent(step);

          return (
            <React.Fragment key={index}>
              {index > 0 && <div className={getConnectorClass(step.status, prevStep?.status)} />}

              <div className={getStepCircleClass(step.status)}>
                {icon}
                <span className={clsx('absolute -bottom-7 whitespace-nowrap text-sm max-md:text-xs', textClass)}>
                  {step.title}
                </span>
              </div>
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
}
