// TODO: these shits must be replaced by Carform logic
// TODO: as soon as possible


// import PlateInputMotor from "./ReadOnly/PlateInputMotor";
import PlateInputCar from "./ReadOnly/PlateInputCar";

type Props = {
    left: string;
    right: string;
    middle: string | undefined;
    alphabet: string | undefined;
    isMotor: boolean;
}


export default function DisplayPlaque({left, middle, alphabet, right, isMotor}: Props) {
    return (
        <>
            {
                isMotor ? (
                    // <PlateInputMotor readOnly value={[left, right]}/>
                    <></>
                ) : (<PlateInputCar readOnly value={[left, alphabet!, middle!, right]}/>)
            }
        </>
    );
}
