import React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  width?: number | string;
  height?: number | string;
  fill?: string;
}

const WithdrawIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  fill = "none",
  className,
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    className={className}
    {...props}
  >
    <path
      d="M16.75,4.75h.75A.75.75,0,0,0,16.75,4Zm-12,0V4A.75.75,0,0,0,4,4.75Zm0-4v0Zm12,0v0Zm-.75,4v12h1.5v-12ZM14.75,18h-8v1.5h8ZM5.5,16.75v-12H4v12ZM4.75,5.5h12V4h-12Zm0-4h12V0h-12ZM1.5,4.75A3.25,3.25,0,0,1,4.75,1.5V0A4.75,4.75,0,0,0,0,4.75ZM4.75,8A3.25,3.25,0,0,1,1.5,4.75H0A4.75,4.75,0,0,0,4.75,9.5ZM20,4.75A3.25,3.25,0,0,1,16.75,8V9.5A4.75,4.75,0,0,0,21.5,4.75Zm1.5,0A4.75,4.75,0,0,0,16.75,0V1.5A3.25,3.25,0,0,1,20,4.75Zm-5.5,12A1.25,1.25,0,0,1,14.75,18v1.5a2.75,2.75,0,0,0,2.75-2.75ZM6.75,18A1.25,1.25,0,0,1,5.5,16.75H4A2.75,2.75,0,0,0,6.75,19.5Z"
      transform="translate(1.25 2.25)"
      fill="currentColor"
    />
    <path
      d="M5.28,5.28A.75.75,0,0,0,4.22,4.22Zm-1.823.763.53.53Zm-1.414,0,.53-.53h0ZM1.28,4.22A.75.75,0,0,0,.22,5.28ZM3.5.75A.75.75,0,0,0,2,.75ZM2,5.75a.75.75,0,0,0,1.5,0ZM4.22,4.22,2.927,5.513,3.987,6.573,5.28,5.28ZM2.573,5.513,1.28,4.22.22,5.28,1.513,6.573Zm.354,0a.25.25,0,0,1-.354,0L1.513,6.573a1.75,1.75,0,0,0,2.475,0ZM2,.75v5H3.5v-5Z"
      transform="translate(9.25 10.25)"
      fill="currentColor"
    />
  </svg>
);

export default WithdrawIcon;
