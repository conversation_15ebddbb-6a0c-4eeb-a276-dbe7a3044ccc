"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { deleteCookie } from "cookies-next";
import { UserType, AuthContextType } from "@/lib/types/auth-types";

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<UserType | null>(null);
  const [loading, setLoading] = useState(true);

  const loadUser = async () => {
    setLoading(true);

    const response = await fetch("/api/auth/me", { credentials: "include" });
    const result = await response.json();

    // Token missing or invalid -> clear user + clear cookie
    if (!result || response.status === 401) {
      deleteCookie("access_token");
      setUser(null);
      setLoading(false);
      return;
    }

    // Server error
    if (response.status >= 500) {
      setUser(null);
      setLoading(false);
      return;
    }

    // Valid user
    setUser(result.data);
    setLoading(false);
  };

  useEffect(() => {
    loadUser();
  }, []);

  const logout = async () => {
    deleteCookie("access_token");
    setUser(null);
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        isAuthenticated: !!user,
        logout,
        refresh: loadUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const ctx = useContext(AuthContext);
  if (!ctx) {
    throw new Error("useAuth must be used inside <AuthProvider>");
  }
  return ctx;
}
