# Mobile Filter System for Insurance Category

This directory contains a comprehensive mobile-first filter system designed specifically for the insurance category page. The implementation follows modern UX patterns and provides an excellent mobile experience.

## Components Overview

### 1. MobileFilterDrawer.tsx
A full-featured mobile filter drawer that slides up from the bottom of the screen.

**Features:**
- Smooth slide-up animation with backdrop blur
- Sticky header with drag handle and close button
- Scrollable content area with custom scrollbar
- All filter options from desktop version
- Apply/Reset buttons at bottom
- Prevents body scroll when open
- Active filter count display

**Usage:**
```tsx
<MobileFilterDrawer
  isOpen={isMobileFilterOpen}
  onClose={() => setIsMobileFilterOpen(false)}
  onApplyFilters={handleApplyFilters}
  onResetFilters={handleResetFilters}
/>
```

### 2. MobileFilterButton.tsx
A floating action button (FAB) that opens the mobile filter drawer.

**Features:**
- Fixed positioning at bottom-right
- Badge showing active filter count
- Smooth hover and press animations
- Material Design inspired
- Only visible on mobile devices

**Usage:**
```tsx
<MobileFilterButton
  onClick={() => setIsMobileFilterOpen(true)}
  activeFiltersCount={activeFiltersCount}
/>
```

### 3. Enhanced Sortbar.tsx
Updated sortbar with improved mobile functionality.

**Features:**
- Desktop: Horizontal sort options with active state
- Mobile: Dropdown with selected option display
- Click outside to close dropdown
- Smooth animations and transitions
- Results count display

**Usage:**
```tsx
<Sortbar 
  onSortChange={handleSortChange} 
  resultCount={31} 
/>
```

### 4. FilterSection.tsx
A responsive wrapper that hides desktop filters on mobile.

**Features:**
- Shows content only on desktop
- Maintains consistent spacing
- Clean separation of concerns

**Usage:**
```tsx
<FilterSection className="col-span-3">
  {/* Desktop filter content */}
</FilterSection>
```

## Design Principles

### 1. Mobile-First Approach
- Designed primarily for mobile experience
- Desktop functionality maintained
- Progressive enhancement

### 2. Accessibility
- Proper ARIA labels
- Keyboard navigation support
- Touch-friendly targets (44px minimum)
- Screen reader compatibility

### 3. Performance
- Smooth 60fps animations
- Efficient re-renders
- Minimal bundle impact
- CSS-based animations where possible

### 4. UX Best Practices
- Familiar interaction patterns
- Clear visual hierarchy
- Immediate feedback
- Consistent with app design language

## Technical Implementation

### State Management
```tsx
const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);
const [activeFiltersCount, setActiveFiltersCount] = useState(0);
const [sortValue, setSortValue] = useState("all");
```

### Event Handlers
```tsx
const handleApplyFilters = () => {
  // Apply filter logic
  onApplyFilters?.();
  onClose();
};

const handleResetFilters = () => {
  setActiveFiltersCount(0);
  onResetFilters?.();
};
```

### Responsive Behavior
- **Desktop (≥768px)**: Traditional sidebar filters + horizontal sort options
- **Mobile (<768px)**: Hidden sidebar + floating filter button + drawer + dropdown sort

## Styling

### CSS Classes Used
- Tailwind CSS for responsive design
- Custom CSS for animations (MobileFilterStyles.css)
- Consistent with existing design system

### Key Animations
- Drawer slide-up: `transform: translateY(100%)` → `translateY(0)`
- Backdrop fade: `opacity: 0` → `opacity: 1`
- Button hover: Scale and shadow effects
- Dropdown: Smooth height transitions

## Integration Guide

### 1. Import Components
```tsx
import MobileFilterDrawer from "@/components/Insurance/Category/MobileFilterDrawer";
import MobileFilterButton from "@/components/Insurance/Category/MobileFilterButton";
import FilterSection from "@/components/Insurance/Category/FilterSection";
```

### 2. Add State Management
```tsx
const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);
const [activeFiltersCount, setActiveFiltersCount] = useState(0);
```

### 3. Wrap Desktop Filters
```tsx
<FilterSection className="col-span-3">
  {/* Existing desktop filter content */}
</FilterSection>
```

### 4. Add Mobile Components
```tsx
<MobileFilterButton
  onClick={() => setIsMobileFilterOpen(true)}
  activeFiltersCount={activeFiltersCount}
/>

<MobileFilterDrawer
  isOpen={isMobileFilterOpen}
  onClose={() => setIsMobileFilterOpen(false)}
  onApplyFilters={handleApplyFilters}
  onResetFilters={handleResetFilters}
/>
```

## Browser Support

- **iOS Safari**: Full support with smooth animations
- **Chrome Mobile**: Full support
- **Firefox Mobile**: Full support
- **Samsung Internet**: Full support
- **Desktop browsers**: Fallback to desktop experience

## Performance Considerations

- Drawer content is rendered but hidden when closed
- Smooth animations use CSS transforms (GPU accelerated)
- Event listeners properly cleaned up
- Body scroll prevention only when needed

## Future Enhancements

1. **Gesture Support**: Swipe down to close drawer
2. **Filter Persistence**: Remember applied filters
3. **Advanced Animations**: Spring-based animations
4. **Voice Search**: Voice input for filter search
5. **Filter Suggestions**: Smart filter recommendations
