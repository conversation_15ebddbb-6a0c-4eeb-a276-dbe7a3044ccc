"use client";

import { Check } from "lucide-react";
import { useState } from "react";
import { useRouter } from "nextjs-toploader/app";

const DamageStatusStep = () => {
    const router = useRouter();
  const [selected, setSelected] = useState<string>("");
  const options = [
    { value: "no", label: "خیر خسارت نگرفته ام" },
    { value: "yes", label: "بله خسارت گرفته ام" },
  ];
  return (
    <div>
      <div className="flex flex-col gap-3">
        <label htmlFor="insurance" className="text-sm font-medium">
          آیا از بیمه نامه قبلی خسارت دریافت کرده اید؟
        </label>

        <div className="flex gap-4">
          {options.map((option) => (
            <label
              key={option.value}
              className={`cursor-pointer flex-1 flex items-center justify-between rounded-2xl border p-4 transition
                            ${
                              selected === option.value
                                ? "border-green-500 bg-green-50 text-green-600"
                                : "border-gray-300 bg-gray-50 text-gray-500"
                            }`}
            >
              <span className="text-sm">{option.label}</span>
              <input
                type="radio"
                name="replacement"
                value={option.value}
                checked={selected === option.value}
                onChange={() => setSelected(option.value)}
                className="hidden"
              />
              {selected === option.value ? (
                <Check className="w-5 h-5 text-green-500" />
              ) : (
                <span className="w-5 h-5 rounded-full border border-gray-300"></span>
              )}
            </label>
          ))}
        </div>
      </div>
      <div className="col-span-2 flex justify-between gap-10 mt-32">
        <button
          onClick={() => router.push("/bime-car-info?step=2")}
          className="w-1/2 border rounded-xl p-3"
        >
          مرحله قبل
        </button>
        <button
          onClick={() => router.push("/bime-form")}
          className="w-1/2 bg-primary text-white rounded-xl p-3"
        >
          تایید و تکمیل
        </button>
      </div>
    </div>
  );
};

export default DamageStatusStep;
