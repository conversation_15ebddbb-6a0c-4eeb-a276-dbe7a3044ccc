"use client"
import JalaliDatePicker from '@/components/UI/JalaliDatePicker';
import { ChevronDown } from 'lucide-react'
import React from 'react'
import { useRouter } from 'nextjs-toploader/app';

const InsuranceDetailStep = () => {
    const [formData, setFormData] = React.useState({
        shamsi_birth_date: '',
    });
    const router = useRouter();

    return (
        <div className='grid grid-cols-2 gap-4'>
            <div className="flex flex-col gap-3">
                <label htmlFor="insurance" className="text-sm font-medium">
                    شرکت بیمه گر قبلی
                </label>

                <div className="relative w-full">
                    <select

                        id="insurance"
                        className="appearance-none w-full h-12 border bg-gray-50 border-gray-300 rounded-xl px-3 pr-4 text-sm leading-none 
                 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-none"
                    >
                        <option value="">انتخاب کنید</option>
                        <option value="iran">بیمه ایران</option>
                        <option value="saman">بیمه سامان</option>
                        <option value="dana">بیمه دانا</option>
                        <option value="parsian">بیمه پارسیان</option>

                    </select>


                    <div className="pointer-events-none absolute inset-y-0 left-3 flex items-center">
                        <ChevronDown className="w-4 h-4 text-gray-500" />
                    </div>
                </div>
            </div>
            <div className="flex flex-col gap-3">
                <label htmlFor="insurance" className="text-sm font-medium">
                    نوع بیمه‌نامه قبلی
                </label>

                <div className="relative w-full">
                    <select

                        id="insurance"
                        className="appearance-none w-full h-12 border bg-gray-50 border-gray-300 rounded-xl px-3 pr-4 text-sm leading-none 
                 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-none"
                    >
                        <option value="">انتخاب کنید</option>
                        <option value="1year">یکساله</option>
                        <option value="2year">دو ساله</option>
                        <option value="3year">سه ساله</option>
                        <option value="4year">چهار ساله</option>
                        <option value="5year">پنج ساله</option>

                    </select>


                    <div className="pointer-events-none absolute inset-y-0 left-3 flex items-center">
                        <ChevronDown className="w-4 h-4 text-gray-500" />
                    </div>
                </div>
            </div>
            <div className="flex flex-col gap-3 col-span-2">
                <label htmlFor="shamsi_birth_date" className="text-sm font-medium">تاریخ اتمام بیمه نامه قبلی</label>
                <JalaliDatePicker
                    value={formData.shamsi_birth_date || ''}
                    setValue={(val) =>
                        setFormData(prev => ({ ...prev, shamsi_birth_date: val?.toString?.() ?? '' }))
                    }
                // disabled={isSubmitting}
                />
            </div>
            <div className="flex flex-col gap-3 max-md:col-span-2">
                <label htmlFor="insurance" className="text-sm font-medium">
                    درصد تخفیف ثالث روی بیمه نامه 
                </label>

                <div className="relative w-full">
                    <select

                        id="insurance"
                        className="appearance-none w-full h-12 border bg-gray-50 border-gray-300 rounded-xl px-3 pr-4 text-sm leading-none 
                 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-none"
                    >
                        <option value="">انتخاب کنید</option>
                        <option value="5">5 درصد</option>
                        <option value="10">10 درصد</option>
                        <option value="15">15 درصد</option>
                        <option value="20">20 درصد</option>
                        <option value="25">25 درصد</option>

                    </select>


                    <div className="pointer-events-none absolute inset-y-0 left-3 flex items-center">
                        <ChevronDown className="w-4 h-4 text-gray-500" />
                    </div>
                </div>
            </div>
            <div className="flex flex-col gap-3 max-md:col-span-2">
                <label htmlFor="insurance" className="text-sm font-medium">
                    درصد تخفیف حوادث راننده روی بیمه‌نامه 
                </label>

                <div className="relative w-full">
                    <select

                        id="insurance"
                        className="appearance-none w-full h-12 border bg-gray-50 border-gray-300 rounded-xl px-3 pr-4 text-sm leading-none 
                 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-none"
                    >
                        <option value="">انتخاب کنید</option>
                        <option value="5">5 درصد</option>
                        <option value="10">10 درصد</option>
                        <option value="15">15 درصد</option>
                        <option value="20">20 درصد</option>
                        <option value="25">25 درصد</option>

                    </select>


                    <div className="pointer-events-none absolute inset-y-0 left-3 flex items-center">
                        <ChevronDown className="w-4 h-4 text-gray-500" />
                    </div>
                </div>
            </div>
             <div className="bg-[#fff5d8b1] col-span-2 p-2 border-2 border-[#f7bb0670] border-dashed max-md:text-sm leading-8 rounded-xl"
                 style={{
                        backgroundImage: "url('/assets/images/alert-triangl.png')",
                        backgroundRepeat: "no-repeat",
                        backgroundPosition: "25px center",
                    }}
            >
                <p className="mb-10">
                    طبق بخشنامه بیمه مرکزی، در صورت تغییر مالکیت در مدت بیمه‌نامه قبلی تنها در شرایط زیر امکان انتقال تخفیفات بیمه‌نامه وجود دارد                   
                </p>
                
            </div>
            <div className='col-span-2 flex justify-between gap-10'>
                <button onClick={() => router.push("/bime-car-info?step=2")} className='w-1/2 border rounded-xl p-3'>
                    مرحله قبل
                </button>
                <button onClick={() => router.push("/bime-car-info?step=4")} className='w-1/2 bg-primary text-white rounded-xl p-3'>
                    تایید و ادامه
                </button>
            </div>
        </div>
    )
}

export default InsuranceDetailStep