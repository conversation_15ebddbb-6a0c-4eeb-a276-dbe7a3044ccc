'use client'
import { useState } from 'react';
import MoneyBagIcon from "@/components/Common/svg/MoneyBagIcon";
import PaymentItem from './PaymentItem';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import CustomButton from '@/components/UI/CustomButton';
import { useRouter } from "nextjs-toploader/app"; 

const PaymentMethod = () => {
    const router = useRouter();
    const [selected, setSelected] = useState<string>("online");

    // useEffect(() => {
    //     if (onPaymentMethodChange) {
    //         onPaymentMethodChange(selected);
    //     }
    // }, [selected, onPaymentMethodChange]);
    return (
        <section className='md:grid grid-cols-12 gap-5 mb-10'>
            <div className='max-md:w-full md:col-span-8 bg-white rounded-3xl md:p-8 max-md:p-5 border max-md:min-h-80'>

                <div className="header flex justify-between">
                    <div className='flex flex-col gap-3 relative pb-5 title-bt-border'>
                        <h2 className='text-xl font-black max-md:text-base max-md:flex max-md:items-center gap-2'>
                            <Link href={'/bime-form?step=3'}>
                                <ArrowRight size={24} className='md:hidden' />
                            </Link>    روش پرداخت
                        </h2>
                        <span className='text-gray-400 font-thin max-md:text-sm'>روش پرداخت مورد نظرتان را انتخاب کنید</span>
                    </div>
                    <div>
                        <MoneyBagIcon className='max-md:w-8' width={70} height={70} />
                    </div>
                </div>
                <p className='text-sm mt-5 text-gray-400 leading-7 max-md:text-xs max-md:leading-5'>
                    بانک مورد نظرتان را انتخاب و یا پرداخت را از کیف پول خود پرداخت کنید
                </p>

                {/* Show wallet payment option if user has balance */}
                <PaymentItem id="wallet" selected={selected} onSelect={setSelected} />


                {/* Always show online payment option */}
                <PaymentItem id="online" selected={selected} onSelect={setSelected} />
            </div>
            <>
                {/* Sticky Bar - Mobile Only */}
                <div className={`fixed bottom-0 left-0 right-0 z-[10] md:hidden bg-white border-t  px-2 py-3.5 flex flex-col gap-5 items-center justify-between overflow-auto`}>
                    <div className="w-full px-1 flex items-center justify-between gap-3 text-sm font-medium">


                        <div className="flex justify-between !font-black w-full text-base px-2">
                            <span>قابل پرداخت</span>
                            <span> 10,000 تومان</span>
                        </div>


                    </div>

                    <div className="w-[95%] mx-auto">
                        <CustomButton

                            className="py-5 px-4 max-md:py-3.5 max-md:px-1 max-md:text-sm"
                        // disabled={isNavigating || cartItems.length === 0}
                        >
                            {/* {isNavigating ? (
                                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                ) : ( */}
                            تایید و پرداخت
                            {/* )} */}
                        </CustomButton>
                    </div>

                </div>

                {/* Full Cart - Desktop Only */}
                <div className={`max-md:hidden border md:col-span-4 bg-white rounded-3xl p-5 max-md:h-[24rem] cart-circles half-circle`}>
                    <div className="relative title-bt-border w-fit pb-5 mt-5">
                        <h2 className='max-md:text-base'>مجموع کل سبد خرید</h2>
                    </div>

                    <div className="flex flex-wrap justify-between mt-7  pb-4 max-md:text-sm">
                        <div className='flex justify-between w-full mb-5 -2 pb-4'>
                            <span>حق بیمه</span>
                            <span>10,000 تومان</span>

                        </div>
                        <div className='flex justify-between w-full -2 pb-4 text-red-400'>
                            <span>سود شما از خرید</span>
                            <span>10,000 تومان </span>

                        </div>
                        <div className='flex justify-between w-full -2 py-4'>
                            <span>قابل پرداخت</span>
                            <span>10,000 تومان </span>

                        </div>
                    </div>



                    <div className=" max-md:text-sm">
                        {/* {
                            paymentMethod === 'wallet' ? (
                                <>
                                    <div className="flex justify-between mb-5  pb-4">
                                        <span>موجودی کیف پول</span>
                                        <span>{formatAmountForDisplay(userData?.balance)} تومان</span>
                                    </div>

                                    <div className="flex justify-between max-md:hidden !font-black">
                                        <span className='!font-black'>قابل پرداخت</span>
                                        <span>{getPayableAfterWallet(finalPrice, userData?.balance)} تومان</span>
                                    </div>

                                </>
                            )
                                :

                                <div className="flex justify-between !font-black">
                                    <span>قابل پرداخت</span>
                                    <span>{finalPrice ? finalPrice.toLocaleString() : "-"} تومان</span>
                                </div>
                        } */}





                        {/* <div className="flex justify-between mb-5">
            <span>قابل پرداخت</span>
            <span>{finalPrice ? finalPrice.toLocaleString() : "-"} تومان</span>
          </div> */}




                        <div className="flex flex-col gap-3 mt-5">
                            <button className="bg-primary text-white rounded-3xl p-3 py-5">
                                تایید و پرداخت
                            </button>
                            <button onClick={() => router.push("/bime-form?step=3")} className="border border-gray-300 rounded-3xl p-3 py-5">
                                مرحله قبل
                            </button>
                        </div>



                    </div>
                </div>
            </>
        </section>
    )
}

export default PaymentMethod