"use server";

import { apiClient } from "@/lib/apiClient";
import { cookies } from "next/headers";

export const sendLoginCode = async (phone: string) => {

    try {
      const response = await apiClient("auth", {
        method: "POST",
        body: { phone },
      });
      
      return await response.json()
    } catch (error:unknown) {
        console.error("Error updating user:", error);
        return { success: false, message: error };
    }
};
export const verifyCode = async (phone: string, code: string, token: string) => {
  try {
    const response: Response = await apiClient("verification", {
      method: "POST",
      body: { phone, code, token },
    });
    
    
    

    
    const setCookieHeader = response.headers.get("Set-Cookie");
    
    
    if (setCookieHeader) {
     
      const cookieParts = setCookieHeader.split(";").map(part => part.trim());
      const [nameValue, ...options] = cookieParts;

      
      const [name, value] = nameValue.split("=");

      
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const cookieOptions: Record<string, any> = {};
      options.forEach(option => {
        const [key, val] = option.split("=");
        cookieOptions[key.toLowerCase()] = val || true; 
      });

      // Convert Max-Age to maxAge (in seconds)
      if (cookieOptions["max-age"]) {
        cookieOptions.maxAge = parseInt(cookieOptions["max-age"], 10);
      }

      
      if (cookieOptions.expires) {
        cookieOptions.expires = new Date(cookieOptions.expires);
      }

      
      (await cookies()).set(name, value, {
        path: cookieOptions.path || "/",
        maxAge: cookieOptions.maxAge,
        expires: cookieOptions.expires,
        secure: cookieOptions.secure || false,
        httpOnly: cookieOptions.httponly || true,
        sameSite: cookieOptions.samesite || "lax",
      });
      
      


     
      return { success: true, accessToken: value };
    } 
    return await response.json()
  } catch (error) {
    console.error("Error verifying code:", error);
    return { success: false, error: String(error) };
  }
};


export async function fetchUserData() {
  const cookieStore = await cookies();
  const token = cookieStore.get("access_token")?.value;
  // console.log(token);
  
  if (!token) {
    return { error: "Unauthorized", status: 401 };
  }

  try {
    const response = await apiClient("user", { method: "GET" });

    

    return await response.json();
  } catch (error) {
    console.log(error);
    return { error: "Server error", status: 500 };
  }
}