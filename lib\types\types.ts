export interface PageContentFAQ {
  question: string;
  answer: string;
}

export interface PageContentCategory {
  title: string | null;
  slug: string | null;
}

export interface PageContentResponse {
  id: string
  title: string;
  meta_title: string;
  meta_description: string;
  description: string;
  schema: string;
  tags: string[];
  faqs: PageContentFAQ[];
  meta_search: string | null;
  canonical: string | null;
  comments: any[]; 
  comments_count: number;
  category: PageContentCategory;
  date_ago: string;
  author_fullname: string;
}

export type ServiceStatusType = string
export type ServiceColorVariantType = 'blue' | 'green' | 'emerald' | 'purple' | 'yellow' | 'indigo' | 'lime' | 'red';
