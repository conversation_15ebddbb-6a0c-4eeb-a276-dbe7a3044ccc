import Container from '@/components/Common/Container'
// import DisplayPlaque from '@/components/Insurance/PlateForms/DisplayPlaque'
import CarInfoStep from '@/components/Insurance/DetailsCompletion/CarInfoStep'
import DamageStatusStep from '@/components/Insurance/DetailsCompletion/DamageStatusStep'
import InsuranceDetailStep from '@/components/Insurance/DetailsCompletion/InsuranceDetailStep'
import InsuranceStatusStep from '@/components/Insurance/DetailsCompletion/InsuranceStatusStep'
import StepBar from '@/components/Insurance/StepBar'
import { PencilLine } from 'lucide-react'
import React from 'react'

const page = async ({ searchParams }: { searchParams: Promise<{ step?: string }>}) => {
     const { step } = await searchParams;
  const currentStep = step ?? "1";

//   // Validate step parameter
//   if (!isValidStep(currentStep)) {
//     redirect('/add-product?step=1');
//   }

  // Generate steps with proper status
//   const steps = generateProductSteps(currentStep);

  /**
   * Render the current step component
   */
  const renderCurrentStep = () => {
    switch (currentStep) {
      case "1":
        return <CarInfoStep />;
      case "2":
        return <InsuranceStatusStep />;
        // return <ProductFormContainer />;
      case "3":
        return <InsuranceDetailStep />;
      case "4":
        return <DamageStatusStep />;
      case "5":
        // return <TermsAndPoliciesContainer />;
      default:
        return <CarInfoStep />;
    }
  };
    return (
        <div>
            <Container className='md:px-4 min-h-[85svh] max-md:w-full max-md:px-0'>
                <section className='mt-10 flex flex-col gap-10 max-md:w-full max-md:px-3'>
                    <div className='md:w-[48rem] md:mx-auto bg-white p-5 rounded-3xl border  flex flex-col gap-5'>
                        <h1 className='text-center text-xl'>
                            اطلاعات خودرو شما
                        </h1>
                        <div className='md:w-96 md:max-w-xs max-md:w-full md:mx-auto'>
                            <StepBar steps={4} currentStep={Number(currentStep)} />
                        </div>
                    </div>
                    <div className='md:w-[48rem] md:mx-auto bg-white md:p-10 max-md:p-3 max-md:py-5 rounded-3xl border'>
                        {renderCurrentStep()}
                        
                    </div>
                </section>
            </Container>
        </div>
    )
}

export default page