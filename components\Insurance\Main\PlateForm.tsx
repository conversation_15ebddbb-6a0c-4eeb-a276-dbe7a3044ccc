"use client"
import CarForm from '../PlateForms/CarForm';
import CustomButton from '@/components/UI/CustomButton';
import CustomInput from '@/components/UI/CustomInput';
import { IdCardIcon } from 'lucide-react';
import React, { ChangeEvent, RefObject, useEffect, useRef, useState, type KeyboardEvent } from 'react'
const persianAlphabets = [
    "ا",
    "ب",
    "پ",
    "ت",
    "ث",
    "ج",
    "چ",
    "ح",
    "خ",
    "د",
    "ذ",
    "ر",
    "ز",
    "ژ",
    "س",
    "ش",
    "ص",
    "ض",
    "ط",
    "ظ",
    "ع",
    "غ",
    "ف",
    "ق",
    "ک",
    "گ",
    "ل",
    "م",
    "ن",
    "و",
    "ه",
    "ی",
    "D",
    "S"
];
const PlateForm = () => {

    const [selectedOption, setSelectedOption] = useState("withoutDetails");
    const [nationalCode, setNationalCode] = useState("")
    // NC == National Code == code meli
    const [NCIsValid, setNCIsValid] = useState<boolean | null>(null);
    const [selectedAlphabet, setSelectedAlphabet] = useState<string | null>(null);
    const input1Ref = useRef<HTMLInputElement | null>(null);
    const input2Ref = useRef<HTMLInputElement | null>(null);
    const input3Ref = useRef<HTMLInputElement | null>(null);
    const selectRef = useRef<HTMLSelectElement | null>(null)
    const nationalCodeRef = useRef<HTMLInputElement | null>(null)
    const [isLoading, setIsLoading] = useState(false)

    // useEffect(() => {
    //     // if (selectedOption === "withDetails") {
    //     //     nationalCodeRef.current?.focus();
    //     // }
    //     console.log(input1Ref.current?.value);
    //     console.log(input2Ref.current?.value);
    //     console.log(input3Ref.current?.value);

    // }, [input1Ref.current?.value, input2Ref.current?.value, input3Ref.current?.value, selectRef.current?.value, nationalCodeRef]);

    const handleInput =
        (
            currentInput: RefObject<HTMLInputElement | null>,
            nextInput: RefObject<HTMLInputElement | HTMLSelectElement | null>
        ) =>
            (e: ChangeEvent<HTMLInputElement>) => {

                e.target.value = e.target.value.replace(/\D/g, "");
                if (e.target.value.length > e.target.maxLength) {
                    e.target.value = e.target.value.slice(0, e.target.maxLength);
                }
                if (e.target.value.length >= e.target.maxLength && nextInput?.current) {
                    nextInput.current.focus();
                }
            };

    const handleSelectInput =
        (nextInput: RefObject<HTMLInputElement | null>) => () => {
            if (nextInput?.current) {
                nextInput.current.focus();
            }
        };

    const handleKeyDown =
        (
            currentInput: RefObject<HTMLInputElement | null>,
            previousInput: RefObject<HTMLInputElement | HTMLSelectElement | null>
        ) =>
            (e: KeyboardEvent<HTMLInputElement>) => {
                if (
                    e.key === "Backspace" &&
                    currentInput.current?.value === "" &&
                    previousInput?.current
                ) {
                    previousInput.current.focus();
                }
            };

    return (
        <div className='flex flex-col gap-8'>
            <CarForm
                persianAlphabets={persianAlphabets}
                selectedAlphabet={selectedAlphabet}
                setSelectedAlphabet={setSelectedAlphabet}
                input1Ref={input1Ref}
                input2Ref={input2Ref}
                input3Ref={input3Ref}
                selectRef={selectRef}
                handleInput={handleInput}
                handleSelectInput={handleSelectInput}
                handleKeyDown={handleKeyDown}
                nationalCodeRef={nationalCodeRef}
                selectedOption={selectedOption}
            />
            <div>
                <CustomInput
                    variant="secondary"
                    allowOnlyNumbers
                    maxLength={10}
                    placeholder="کد ملی مالک را وارد کنید"
                    // {...field}
                    ref={nationalCodeRef}
                    leftIcon={<IdCardIcon height={20} width={20} />}
                    direction="rtl"
                    inputMode="numeric"
                    autoFocus
                    value={nationalCode}
                    onChange={(value) => setNationalCode(value)}
                    
                />
                <p className='px-1 font-light text-sm mt-3'>
                    کد ملی جهت استعلام از بیمه مرکزی الزامیست.توضیحات بیشتر
                </p>

            </div>
            <CustomButton href='/bime-category' className='py-4'>
                خرید و تمدید بیمه
            </CustomButton>
        </div>
    )
}

export default PlateForm