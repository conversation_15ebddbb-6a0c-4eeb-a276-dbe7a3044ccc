"use client"
import DisplayPlaque from '../PlateForms/DisplayPlaque'
import { PencilLine } from 'lucide-react'
import { useRouter } from 'nextjs-toploader/app'
import React from 'react'

const CarInfoStep = () => {
    const router = useRouter()
    return (
        <>
            <div className='bg-muted p-5 rounded-3xl'>
                <div className='max-w-lg mx-auto'>
                    <DisplayPlaque isMotor={false} left="12" alphabet="ب" middle="193" right="21" />
                    <div className='flex flex-col gap-5 font-light mt-8'>
                        <div className='flex justify-between'>
                            <span>
                                نوع خودرو
                            </span>
                            <span>
                                سواری
                            </span>
                        </div>
                        <div className='flex justify-between'>
                            <span>
                                برند خودرو
                            </span>
                            <span>
                                پژو
                            </span>
                        </div>
                        <div className='flex justify-between'>
                            <span>
                                مدل خودرو
                            </span>
                            <span>
                                206
                            </span>
                        </div>
                        <div className='flex justify-between'>
                            <span>
                                کاربری
                            </span>
                            <span>
                                شخصی
                            </span>
                        </div>
                        <div className='flex justify-between'>
                            <span>
                                نوع سوخت
                            </span>
                            <span>
                                بنزین
                            </span>
                        </div>
                        <div className='flex justify-between'>
                            <span>
                                سال ساخت
                            </span>
                            <span>
                                1394
                            </span>
                        </div>
                    </div>
                    <div className='flex justify-center mt-10'>
                        <button className="bg-white text-primary border border-primary rounded-3xl p-3 px-4 flex items-center gap-2 transition-all duration-200 ease-in-out hover:bg-primary hover:text-white hover:shadow-lg hover:-translate-y-1 active:scale-95">
                            <PencilLine /> ویرایش اطلاعات
                        </button>
                    </div>
                </div>
            </div>
            <div className='flex justify-between gap-10 mt-10'>
                <button className='w-1/2 border rounded-xl p-3'>
                    مرحله قبل
                </button>
                <button onClick={() => router.push("/bime-car-info?step=2")} className='w-1/2 bg-primary text-white rounded-xl p-3'>
                    تایید و ادامه
                </button>
            </div>
        </>
    )
}

export default CarInfoStep