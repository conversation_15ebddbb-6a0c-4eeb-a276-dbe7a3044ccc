
"use client"
import SpecialPrice from '@/components/Common/SpecialPrice'
import { <PERSON><PERSON><PERSON>, Pin, Star } from 'lucide-react'
import Image from 'next/image'
import Iran from "@/public/assets/images/IRAN.webp"
import { useRouter } from 'nextjs-toploader/app'

const InsuranceItem = () => {
    const router = useRouter()
    return (
        <div className="flex justify-between max-md:flex-wrap bg-white p-5 max-md:px-3 rounded-3xl border">
            <div className="w-[15%] max-md:w-[25%] max-md:h-full relative p-5 border-2 border-dashed rounded-2xl">
                <Image src={Iran} alt='iran' className="w-full max-md:w-32 max-md:h-12" width={300} height={300} />
            </div>
            <div className="w-[50%]  max-md:w-[73%]">
                <div className="flex max-md:flex-col pb-3 md:justify-between md:items-center max-md:gap-5">
                    <h2 className="title-border relative pb-1">
                        بیمه ایران
                    </h2>
                    <div className="flex items-center gap-3 text-sm font-light max-md:text-xs">
                        <span className="bg-[#E0F3E6] px-2 py-1 text-[#2DC058] rounded-3xl">
                            امکان خرید قسطی
                        </span>
                        <span className="bg-[#FFF7DF] px-2 py-1 text-[#F7BC06] rounded-3xl">
                            بدون پیش پرداخت
                        </span>
                    </div>
                </div>
                <div className="font-light mt-3 max-md:hidden">
                    <p className="flex items-center gap-2">
                        <CreditCard color="#9da5b0" size={18} />   امکان پرداخت خسارت سیار (تمامی شهر ها)
                    </p>
                    <div className="flex gap-5 mt-3">
                        <p className="flex items-center gap-2">
                            <Pin color="#9da5b0" size={18} />  <span className="font-bold">58</span> شعبه
                        </p>
                        <p className="flex items-center gap-2">
                            <Star size={18} fill='#F7BC06' color='#F7BC06' />  3 از 5 تعهد در پرداخت
                        </p>
                    </div>
                </div>
            </div>
             <div className="font-light mt-3 md:hidden my-3 max-md:text-sm">
                    <p className="flex items-center gap-2">
                        <CreditCard color="#9da5b0" size={18} />   امکان پرداخت خسارت سیار (تمامی شهر ها)
                    </p>
                    <div className="flex gap-5 mt-3">
                        <p className="flex items-center gap-2">
                            <Pin color="#9da5b0" size={18} />  <span className="font-bold">58</span> شعبه
                        </p>
                        <p className="flex items-center gap-2">
                            <Star size={18} fill='#F7BC06' color='#F7BC06' />  3 از 5 تعهد در پرداخت
                        </p>
                    </div>
                </div>
            <div className="w-[28%] max-md:w-full bg-gray-50 rounded-3xl max-md:rounded-2xl p-3 flex justify-center items-center flex-col gap-3">
                <div className="flex gap-1 items-center">
                    <SpecialPrice price={798545646} className="text-sm" />
                    <span className="bg-red-500 text-white p-[2px] text-xs rounded-bl-full rounded-tl-full rounded-tr-full ">10%</span>
                </div>
                <div className="flex justify-between w-full items-center">
                    <span>مبلغ نهایی: </span>
                    <p>
                        <span>8,029,000</span> تومان
                    </p>
                </div>
                <button onClick={() => router.push("/bime-car-info")} className="w-full bg-primary text-white p-3 py-2 rounded-xl  font-light">خرید بیمه</button>
            </div>
        </div>
    )
}

export default InsuranceItem