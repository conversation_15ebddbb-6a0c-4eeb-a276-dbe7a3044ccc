"use client";

import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";

import Logo from "@/public/assets/images/logo.png";
import { Minus } from "lucide-react";
// import { useServiceStatus } from "@/lib/providers/ServicesProvider";
import { getDashboardMenuItems } from "@/lib/helpers/menuItems";

type Props = {
    onClose?: () => void;
}

const NavbarItems = ({ onClose }: Props) => {
    const pathname = usePathname(); // Get current URL

    // const { data } = useServiceStatus()

    const services_status = {}
    // console.log(services_status);

    const menuItems = getDashboardMenuItems(services_status)

    return (
        <div
            className="dashboard-sidbar md:w-[17rem] px-6 md:py-7 bg-white h-auto md:shadow-md rounded-xl md:absolute md:top-3 md:right-1.5 z-10 flex flex-col gap-9">
            <div className="w-[90%] h-fit">
                <Image src={Logo}
                    alt="logo"
                    width={128}
                    height={64} 
                    className="h-auto" 
                    priority />
            </div>
            <ul className="flex flex-col gap-3">
                {menuItems
                    .filter(item => item.status === 'ACTIVE' && (item.type === 'mobile' || item.type === 'both'))
                    .map((item) => (
                        (
                            <li key={item.href} className={pathname === item.href ? "active" : ""}
                                onClick={() => onClose?.()}>
                                <Link href={item.href} className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <Minus />
                                        <span className="relative text-sm text-[#363A3E]">
                                            <span
                                                className={`transition-all duration-100 ${pathname === item.href ? 'text-yellow font-semibold' : ''}`}>
                                                {item.title}
                                            </span>
                                        </span>
                                    </div>
                                </Link>
                            </li>
                        )
                    ))}
            </ul>
        </div>
    );
};

export default NavbarItems;
