"use client";

import { useState } from "react";
import { Umbrella, BadgePercent, ReceiptText } from "lucide-react";
import MoneyBagIcon from '@/components/Common/svg/MoneyBagIcon'
import WithdrawIcon from '@/components/Common/svg/WithdrawIcon'
const tabs = [
  {
    id: "coverages",
    label: "پوشش ها و تعهدات",
    icon: <Umbrella />,
    content: (
      <p className="text-justify font-light leading-8">
        بیمه شخص ثالث، به دلیل اجباری بودن برای خودروها، بازار گسترده‌ای دارد.
        این بیمه مالکان خودرو را در برابر خسارات مالی و جانی وارد به اشخاص ثالث
        حمایت می‌کند. خسارت مالی شامل آسیب به اموال دیگران و خسارت بدنی شامل
        صدمات جانی مانند فوت یا نقص عضو است.
      </p>
    ),
  },
  {
    id: "pricing",
    label: "محاسبه و قیمت",
    icon: <MoneyBagIcon width={25} height={20} />,
    content: (
      <p className="text-justify font-light leading-8">
        قیمت بیمه شخص ثالث بر اساس نوع خودرو، سابقه خسارت، تخفیفات و مدت زمان
        بیمه‌نامه محاسبه می‌شود. هرچه سال‌های بدون خسارت بیشتر باشد، تخفیف
        بالاتری اعمال می‌گردد.
      </p>
    ),
  },
  {
    id: "installments",
    label: "خرید اقساطی",
    icon: <WithdrawIcon />,
    content: (
      <p className="text-justify font-light leading-8">
        شما می‌توانید بیمه‌نامه خود را به صورت اقساطی خریداری کنید. تعداد اقساط
        و شرایط پرداخت بسته به شرکت بیمه و مبلغ نهایی متفاوت است.
      </p>
    ),
  },
  {
    id: "discounts",
    label: "انتقال تخفیفات",
    icon: <BadgePercent />,
    content: (
      <p className="text-justify font-light leading-8">
        تخفیف عدم خسارت بیمه شخص ثالث قابل انتقال بین خودروها یا افراد است.
        برای انتقال، باید مدارک مالکیت جدید و بیمه‌نامه قبلی ارائه شود.
      </p>
    ),
  },
  {
    id: "details",
    label: "توضیحات تکمیلی",
    icon: <ReceiptText />,
    content: (
      <p className="text-justify font-light leading-8">
        اطلاعات تکمیلی شامل شرایط عمومی بیمه‌نامه، نحوه پرداخت خسارت، و مقررات
        فسخ یا تمدید بیمه است. مطالعه دقیق این بخش قبل از خرید توصیه می‌شود.
      </p>
    ),
  },
];

export default function InsuranceInfoTabs() {
  const [activeTab, setActiveTab] = useState(tabs[0].id);

  const activeContent = tabs.find((tab) => tab.id === activeTab)?.content;

  return (
    <section>
      {/* Top Tabs */}
      <div className="relative -top-14 max-w-5xl mx-auto bg-primary p-5 md:rounded-3xl text-white flex justify-between items-center gap-5 max-md:overflow-x-auto max-md:whitespace-nowrap">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex flex-col gap-3 w-[20%] max-md:min-w-[120px] items-center p-5 rounded-xl transition-all ${
              activeTab === tab.id
                ? "bg-white text-primary shadow-md"
                : " text-white "
            }`}
          >
            {tab.icon}
            <span className="text-sm font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Content Area */}
      <div className="max-w-5xl mx-auto p-5 bg-white rounded-2xl border mb-5">
        <div className="h-[27rem] p-5 overflow-y-auto space-y-3">{activeContent}</div>
      </div>
    </section>
  );
}
