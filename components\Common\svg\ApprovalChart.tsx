import React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
//   width?: number | string;
//   height?: number | string;
  fill?: string;
  size?: number
}

const ApprovalChart: React.FC<IconProps> = ({
  size = 41,
  fill = "#eb5835",
  className,
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 41 27.273"
    className={className}
    {...props}
  >
    <path
      d="M23.2,5.572A20.436,20.436,0,0,0,3.881,32.846,18.332,18.332,0,0,1,31.955,13.433l3.657-3.657A20.5,20.5,0,0,0,23.2,5.572Zm15.629,6.156a1.968,1.968,0,0,0-1.228.48L21.9,25.5c-.722.611-2.419,2.412,0,4.828s4.216.715,4.828-.007l13.3-15.7a1.758,1.758,0,0,0,0-2.416,1.617,1.617,0,0,0-1.188-.48Zm3.16,6.163-3.453,3.453a18.359,18.359,0,0,1,1.548,7.167H43.53a19.6,19.6,0,0,0-1.538-10.62Z"
      fill={fill}
      transform="translate(-2.703 -5.572)"
    />
  </svg>
);

export default ApprovalChart;
