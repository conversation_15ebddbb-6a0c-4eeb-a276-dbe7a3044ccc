import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "www.ikea.com",
        pathname: "/de/en/images/products/**",
      },
      {
        protocol: "https",
        hostname: "files.virgool.io",
        pathname: "/upload/**",
      },
      {
        protocol: "https",
        hostname: "us.e-cloth.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "satvikworld.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "www.satvikworld.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "khodrox.iranisoft.ir",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "picsum.photos",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "shop-khodrox.liara.run",
        pathname: "/**",
      },
       {
        protocol: "https",
        hostname: "dl.khodrox.com",
        pathname: "/**",
      },
    ],
    // Remove deprecated domains property for Next.js 15.5.2   
     domains: ["localhost", "dkstatics-public.digikala.com", "dl.khodrox.com",'trustseal.enamad.ir','s3.ayanco.com', 'nginx'],

  },
};

export default nextConfig;
