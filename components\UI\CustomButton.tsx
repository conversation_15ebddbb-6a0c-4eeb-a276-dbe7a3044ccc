import React, { ReactNode, MouseEvent } from 'react';
import <PERSON> from 'next/link';
// import { BeatLoader } from "react-spinners";

interface ButtonProps {
    children: ReactNode;
    onClick?: (event: MouseEvent<HTMLButtonElement>) => void;
    className?: string;
    bgColor?: string;
    textColor?: string;
    padding?: string;
    disabled?: boolean;
    type?: 'button' | 'submit' | 'reset';
    width?: string;
    loading?: boolean;
    href?: string;
    target?: '_blank' | '_self' | '_parent' | '_top';
    rel?: string;
    variant?: 'primary' | 'green' | 'gray';
    [key: string]: any;
}

const CustomButton: React.FC<ButtonProps> = ({
    children,
    onClick,
    loading,
    className = '',
    bgColor,
    textColor,
    padding = 'py-2.5',
    disabled = false,
    type = 'button',
    width = "w-full",
    href,
    target,
    rel,
    variant = 'primary',
    ...props
}) => {
    const variantStyles = {
        primary: { bg: 'bg-primary', text: 'text-white' },
        green: { bg: 'bg-green-500', text: 'text-white' },
        gray: { bg: 'bg-gray-400', text: 'text-white' },
    };

    const selectedVariant = variantStyles[variant] || variantStyles.primary;

    const bColor = disabled ? 'bg-gray-300' : bgColor || selectedVariant.bg;
    const tColor = disabled ? 'text-gray-500' : textColor || selectedVariant.text;
    const disabledClass = disabled ? 'cursor-not-allowed' : '';

    const combinedClassName =
        `w-full ${bColor} ${tColor} ${disabledClass} ${width} ${padding} 
         rounded-2xl flex items-center gap-2 px-3 justify-center 
         text-xs md:text-sm ${className}`.replace(/\s+/g, ' ').trim();

    if (href) {
        return (
            <Link
                href={href}
                target={target}
                rel={target === '_blank' ? 'noopener noreferrer' : rel}
                className={`block ${combinedClassName}`}
            >
                {loading ? (
                    // <BeatLoader color='white' size={10} />
                    <div className='w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin'></div>
                ) : (
                    children
                )}
            </Link>
        );
    }

    return (
        <button
            type={type}
            className={combinedClassName}
            onClick={onClick}
            disabled={disabled}
            {...props}
        >
            {loading ? (
                <div className='w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin'></div>
            ) : (
                children
            )}
        </button>
    );
};

export default CustomButton;
