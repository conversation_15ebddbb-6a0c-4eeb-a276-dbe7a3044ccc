"use client"
import Image from 'next/image'
import CarCart1 from "@/public/assets/images/car-cart-1.png"
import CarCart2 from "@/public/assets/images/car-cart-2.png"
import CarCart3 from "@/public/assets/images/car-cart-3.png"
import Iran from "@/public/assets/images/IRAN.webp"
import { useRouter } from "nextjs-toploader/app"; 
const InsuranceFianalDetails = () => {
    const router = useRouter();
    return (
        <div className='md:grid mb-10 md:grid-cols-12 gap-4 '>
            <div className='col-span-8 max-md:mb-5'>
                <section className='bg-white p-5 rounded-3xl  border'>
                    <div className='mb-8 w-fit relative title-bt-border pb-5'>
                        <h2 className='text-xl font-bold'>
                            مشخصات بیمه نامه
                        </h2>
                    </div>
                    <div className='flex flex-col gap-3'>
                        <div className='grid grid-cols-3 max-md:grid-cols-2 gap-3 font-light'>
                            <div className='flex flex-col gap-2 bg-muted p-5 rounded-3xl border'>
                                <p>
                                    پوشش مالی:
                                </p>
                                <p>
                                    53.3 میلیون تومان
                                </p>
                            </div>

                            <div className='flex flex-col gap-2 bg-muted p-5 rounded-3xl border'>
                                <p>
                                    پوشش نقص عضو:
                                </p>
                                <p>
                                    53.3 میلیارد تومان
                                </p>
                            </div>


                            <div className='flex flex-col max-md:col-span-2 gap-2 bg-muted p-5 rounded-3xl border'>
                                <p>
                                    پوشش حوادث راننده:
                                </p>
                                <p>
                                    1.3 میلیارد تومان
                                </p>
                            </div>



                        </div>
                        <div className='grid grid-cols-2 max-md:grid-cols-1 gap-3 font-light'>
                            <div className='p-5 bg-muted border border-dashed rounded-3xl flex flex-col gap-3'>
                                <div className='flex justify-between'>
                                    <span>
                                        نوع خودرو
                                    </span>
                                    <span>
                                        سواری
                                    </span>
                                </div>
                                <div className='flex justify-between'>
                                    <span>
                                        برند خودرو
                                    </span>
                                    <span>
                                        پژو
                                    </span>
                                </div>
                                <div className='flex justify-between'>
                                    <span>
                                        مدل خودرو
                                    </span>
                                    <span>
                                        206
                                    </span>
                                </div>
                                <div className='flex justify-between'>
                                    <span>
                                        کاربری
                                    </span>
                                    <span>
                                        شخصی
                                    </span>
                                </div>
                                <div className='flex justify-between'>
                                    <span>
                                        نوع سوخت
                                    </span>
                                    <span>
                                        بنزین
                                    </span>
                                </div>
                                <div className='flex justify-between'>
                                    <span>
                                        سال ساخت
                                    </span>
                                    <span>
                                        1394
                                    </span>
                                </div>

                            </div>
                            <div className='p-5 bg-muted border border-dashed rounded-3xl flex flex-col gap-3'>
                                <div className='flex justify-between'>
                                    <span>
                                        شرکت بیمه گر قبلی
                                    </span>
                                    <span>
                                        بیمه ایران
                                    </span>
                                </div>
                                <div className='flex justify-between'>
                                    <span>
                                        مدت بیمه نامه قبلی
                                    </span>
                                    <span>
                                        یک سال
                                    </span>
                                </div>
                                <div className='flex justify-between'>
                                    <span>
                                        تاریخ اتمام بیمه قبلی
                                    </span>
                                    <span>
                                        1402/01/01
                                    </span>
                                </div>
                                <div className='flex justify-between'>
                                    <span>
                                        تخفیف ثالث قبلی
                                    </span>
                                    <span>
                                        10%
                                    </span>
                                </div>
                                <div className='flex justify-between'>
                                    <span>
                                        تخفیف حوادث راننده قبلی
                                    </span>
                                    <span>
                                        10%
                                    </span>
                                </div>
                                <div className='flex justify-between'>
                                    <span>
                                        جریمه دیرکرد
                                    </span>
                                    <span>
                                        0
                                    </span>
                                </div>

                            </div>
                            <div className='p-5 bg-muted border border-dashed rounded-3xl flex flex-col gap-3'>
                                <div className='flex justify-between'>
                                    <span>
                                        تخفیف ثالث جدید
                                    </span>
                                    <span>
                                        10%
                                    </span>
                                </div>
                            </div>
                            <div className='p-5 bg-muted border border-dashed rounded-3xl flex flex-col gap-3'>
                                <div className='flex justify-between'>
                                    <span>
                                        تخفیف حوادث راننده جدید
                                    </span>
                                    <span>
                                        10%
                                    </span>
                                </div>
                            </div>

                        </div>
                        <div className='grid grid-cols-1 gap-3 font-light'>
                            <div className='p-5 bg-muted border border-dashed rounded-3xl flex flex-col gap-3'>
                                <div>
                                    <div className='flex gap-2 items-center'>
                                        <p className='w-2 h-2 bg-gray-700 rounded-full'></p>
                                        <span>
                                            وضعیت بیمه نامه قبلی
                                        </span>
                                    </div>
                                    <p>
                                        <span className='font-bold'>-</span> بیمه نامه دارم با شماره پلاک مالک فعلی خودرو
                                    </p>
                                </div>
                                <div>
                                    <div className='flex gap-2 items-center'>
                                        <p className='w-2 h-2 bg-gray-700 rounded-full'></p>
                                        <span>
                                            آدرس مندرج در بیمه‌نامه
                                        </span>
                                    </div>
                                    <p>
                                        <span className='font-bold'>-</span> تهران / خیابان طالقانی /  کوچه شهید بهشتی / پلاک 41
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <section className='bg-white p-5 rounded-3xl border mt-5'>
                    <div className='mb-8 w-fit relative title-bt-border pb-5'>
                        <h2 className='text-xl font-bold'>
                            اطلاعات و مدارک بیمه گزار
                        </h2>
                    </div>
                    <div className='p-5 max-md:col-span-2 max-md:p-3 max-md:rounded-2xl max-md:py-5 md:col-span-3 bg-muted border border-dashed rounded-3xl font-light insurance-bg'
                    >
                        <div className='mb-5'>
                            <h3 className='font-bold  border-b pb-1 border-gray-300 w-fit'>
                                اطلاعات بیمه گذار
                            </h3>
                        </div>
                        <div className='md:divide-x md:flex md:gap-10 max-md:grid max-md:gap-5 max-md:grid-cols-2'>
                            <div className='flex flex-col gap-2 md:px-10'>
                                <div className='flex gap-2 items-center'>
                                    <p className='w-2 h-2 bg-gray-700 rounded-full'></p>
                                    <span>
                                        نام و نام خانوادگی
                                    </span>
                                </div>
                                <p className='text-gray-500 flex gap-2'>
                                    <span className='font-bold'>-</span> محمد محمدی
                                </p>
                            </div>
                            <div className='flex flex-col gap-2 md:px-10'>
                                <div className='flex gap-2 items-center'>
                                    <p className='w-2 h-2 bg-gray-700 rounded-full'></p>
                                    <span>
                                        کد ملی
                                    </span>
                                </div>
                                <p className='text-gray-500 flex gap-2'>
                                    <span className='font-bold'>-</span> 1278225485
                                </p>
                            </div>

                            <div className='flex flex-col gap-2 md:px-10'>
                                <div className='flex gap-2 items-center'>
                                    <p className='w-2 h-2 bg-gray-700 rounded-full'></p>
                                    <span>
                                        تاریخ تولد
                                    </span>
                                </div>
                                <p className='text-gray-500 flex gap-2'>
                                    <span className='font-bold'>-</span> 1400/01/01
                                </p>
                            </div>
                            <div className='flex flex-col gap-2 md:px-10'>
                                <div className='flex gap-2 items-center'>
                                    <p className='w-2 h-2 bg-gray-700 rounded-full'></p>
                                    <span>
                                        شماره موبایل
                                    </span>
                                </div>
                                <p className='text-gray-500 flex gap-2'>
                                    <span className='font-bold'>-</span> 09123456789
                                </p>
                            </div>

                        </div>

                    </div>

                    <div className='flex justify-between mt-10 text-sm'>
                        <div className='flex flex-col gap-3 items-center'>
                            <Image src={CarCart1} alt='car-cart' />
                            <h2>
                                عکس پشت کارت ماشین یا برگ سبز
                            </h2>
                        </div>
                        <div className='flex flex-col gap-3 items-center'>
                            <Image src={CarCart2} alt='car-cart' />
                            <h2>
                                عکس روی کارت ماشین یا برگ سبز
                            </h2>
                        </div>
                        <div className='flex flex-col gap-3 items-center'>
                            <Image src={CarCart3} alt='car-cart' />
                            <h2 className='text-sm'>
                                عکس روی کارت ماشین یا برگ سبز
                            </h2>
                        </div>
                    </div>

                </section>
            </div>
            <div className="col-span-4">
                <div className="bg-white p-5 rounded-3xl border">
                    <div className="mb-8 w-fit relative title-bt-border pb-5">
                        <h2 className="text-xl font-bold">
                            ادامه خرید بیمه نامه
                        </h2>
                    </div>
                    <div className="p-3 border-2 flex gap-5 items-center border-dashed rounded-3xl divide-x-2">
                        <div className='w-[25%]'>
                            <Image src={Iran} alt="iran" width={55} height={55} />
                        </div>
                        <div className="px-5 space-y-3">
                            <p className="flex gap-2">
                                <span className="font-bold">بیمه گذار: </span>
                                <span>محمد محمدی</span>
                            </p>
                            <p className="flex gap-2">
                                <span className="font-bold">
                                    نوع بیمه:
                                </span>
                                <span>
                                    بیمه شخص ثالث
                                </span>
                            </p>
                        </div>
                    </div>
                    <div className='flex flex-col gap-3 mt-5 px-1'>
                        <div className="flex justify-between">
                            <p>
                                پلاک
                            </p>
                            <p>
                                ایران 25 | 742 الف 45
                            </p>
                        </div>
                        <div className="flex justify-between">
                            <p>
                                مدت بیمه نامه
                            </p>
                            <p>
                                یک سال
                            </p>
                        </div>
                        <div className="flex justify-between">
                            <p>
                                مبلغ بیمه نامه
                            </p>
                            <p>
                                10,000,000 تومان
                            </p>
                        </div>
                    </div>
                    <div className="flex flex-col gap-3 mt-5">
                        <button onClick={() => router.push("/bime-form?step=4")} className="bg-primary text-white rounded-3xl p-3 py-5">
                            تایید و ادامه
                        </button>
                        <button onClick={() => router.push("/bime-form?step=2")} className="border border-gray-300 rounded-3xl p-3 py-5">
                            مرحله قبل
                        </button>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default InsuranceFianalDetails