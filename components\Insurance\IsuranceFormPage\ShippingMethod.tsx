"use client";

import { Check } from "lucide-react";
import { useState } from "react";
import Iran from "@/public/assets/images/IRAN.webp"
import Image from "next/image";
import { useRouter } from "nextjs-toploader/app"; 

const ShippingMethod = () => {
  const router = useRouter();
  const [selected, setSelected] = useState<string>("");

  const options = [
    { value: "no", label: "خیر " },
    { value: "yes", label: "بله " },
  ];
  return (
    <div className="md:grid mb-10 md:grid-cols-12 gap-4 max-md:space-y-5">
      <div className="col-span-8">
        <section className="bg-white p-5 rounded-3xl border">
          <div className="mb-8 w-fit relative title-bt-border pb-5">
            <h2 className="text-xl font-bold ">به نسخه چاپی بیمه‌نامه نیاز دارید؟</h2>
          </div>
          <div className="flex flex-col gap-5">
            <div className="bg-[#fff5d8b1] p-2 border-2 border-[#f7bb0670] border-dashed max-md:text-sm leading-8 rounded-xl">
              <p>
                بر طبق قوانین بیمه مرکزی تمامی امور مربوط به بیمه نامه شخص ثالث
                مانند دریافت و یا پرداخت خسارت با نسخه الکترونیکی انجام میگیرد و
                نیازی به ارائه نسخه کاغذی نمی باشد. همچنین شما می توانید در هر لحظه
                با مراجعه به پروفایل کاربری بیمه نامه خود را دانلود نمایید .آیا با
                ذکر این توضیحات، هنوز به نسخه چاپی بیمه‌نامه ثالث نیاز دارید؟{" "}
              </p>
            </div>
            <div className="flex flex-col gap-3">
              <label htmlFor="insurance" className="text-sm font-medium">
                آیا خودرو شما در مدت بیمه نامه قبلی تعویض پلاک (تغییر مالکیت) داشته
                است؟
              </label>

              <div className="flex gap-4">
                {options.map((option) => (
                  <label
                    key={option.value}
                    className={`cursor-pointer flex-1 flex items-center justify-between rounded-2xl border p-4 transition
                                        ${selected === option.value
                        ? "border-green-500 bg-green-50 text-green-600"
                        : "border-gray-300 bg-gray-50 text-gray-500"
                      }`}
                  >
                    <span className="text-sm">{option.label}</span>
                    <input
                      type="radio"
                      name="replacement"
                      value={option.value}
                      checked={selected === option.value}
                      onChange={() => setSelected(option.value)}
                      className="hidden"
                    />
                    {selected === option.value ? (
                      <Check className="w-5 h-5 text-green-500" />
                    ) : (
                      <span className="w-5 h-5 rounded-full border border-gray-300"></span>
                    )}
                  </label>
                ))}
              </div>
            </div>
            <div className="bg-[#1F84FB]/10 p-2 border-2 border-[#1F84FB]/35 border-dashed max-md:text-sm leading-8 rounded-xl mt-10">
              <p>
                درصورت انتخاب نسخه الکترونیکی مبلغ ۱٬۰۰۰٬۰۰۰ریال کیف پول شما به عنوان هدیه شارژ خواهد شد. توجه داشته باشید این مبلغ بعد از صدور بیمه نامه به کیف پول شما اضافه می گردد.
              </p>
            </div>
          </div>
        </section>
        <section className="mt-10 bg-white p-5 rounded-3xl border">
          <div className="mb-8 w-fit relative title-bt-border pb-5">
            <h2 className="text-xl font-bold">
              توضیح دیگری درباره صدور بیمه دارید؟
            </h2>
          </div>
          <div className="flex flex-col gap-5">
            <textarea
              className="border border-gray-300 bg-gray-50 rounded-3xl p-3"
              placeholder="توضیح خود را وارد کنید ..."
              rows={8}
            />
          </div>
        </section>
      </div>
      <div className="col-span-4">
        <div className="bg-white p-5 rounded-3xl border">
          <div className="mb-8 w-fit relative title-bt-border pb-5">
            <h2 className="text-xl font-bold">
              ادامه خرید بیمه نامه
            </h2>
          </div>
          <div className="p-3 border-2 flex gap-5 items-center border-dashed rounded-3xl  divide-x-2">
            <div className="md:w-[25%]">
              <Image src={Iran} alt="iran" width={55} height={55} />
            </div>
            <div className="px-5 space-y-3">
              <p className="flex gap-2">
                <span className="font-bold">بیمه گذار: </span>
                <span>محمد محمدی</span>
              </p>
              <p className="flex gap-2">
                <span className="font-bold">
                  نوع بیمه:
                </span>
                <span>
                  بیمه شخص ثالث
                </span>
              </p>
            </div>
          </div>
          <div className="flex flex-col gap-3 mt-5">
            <button onClick={() => router.push("/bime-form?step=3")} className="bg-primary text-white rounded-3xl p-3 py-5">
              تایید و ادامه
            </button>
            <button onClick={() => router.push("/bime-form?step=1")} className="border border-gray-300 rounded-3xl p-3 py-5">
              مرحله قبل
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShippingMethod;
